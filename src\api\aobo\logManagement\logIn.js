import request from '@/utils/request'

// 列表
export function getList(data) {
  return request({
    url: '/api/v1/web/loginLog/page',
    method: 'post',
    data
  })
}

// 导出
export function exportLogIn(data) {
  return request({
    url: '/api/v1/web/loginLog/export',
    method: 'post',
    data,
    responseType: 'blob'
  })
}

// 端口下拉列表
// export function portDropDownBox() {
//   return request({
//     url: '/api/v1/web/port/dropDownBox',
//     method: 'get'
//   })
// }
export function portDropDownBox() {
  return request({
    url: '/api/v1/sys/loginLog/portDropDownBox',
    method: 'get'
  })
}
