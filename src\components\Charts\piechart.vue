<template>
  <div
    :id="id"
    :style="{ height: height, width: width }"
  />
</template>
<script>
import chartsMixIn from './mixins'

export default {
  name: '<PERSON><PERSON><PERSON>',
  mixins: [chartsMixIn],
  props: {
    id: {
      require: false,
      type: String,
      default: 'charts'
    },
    width: {
      require: false,
      type: String,
      default: '100%'
    },
    height: {
      require: false,
      type: String,
      default: '100%'
    },
    propData: {
      require: false,
      type: Array,
      default: () => []
    }
  },
  watch: {
    propData(newValue) {
      if (newValue) {
        this.hideLoading()
        if (newValue.length < 1) {
          this.noData()
        }
        if (this.chart) {
          this.chart.clear()
          this.$nextTick(() => {
            this.initChart()
          })
          // console.log(this.propData, newValue)
        }
        this.$nextTick(() => {
          this.initChart()
        })
      }
    },
    deep: true
  },
  created() {
    this.doNotRedraw = true
  },
  mounted() {
    this.initChart()
    // console.log(this.propData, 888888888)
  },
  methods: {
    initChart() {
      this.chart = this.$echarts.init(document.getElementById(this.id))

      this.option = {
        tooltip: {
          trigger: 'item'
        },
        legend: {
          data: []
        },
        series: [
          {
            name: '',
            type: 'pie',
            radius: ['50%', '70%'],
            center: ['50%', '50%'],
            avoidLabelOverlap: false,
            itemStyle: {
              borderRadius: 3,
              borderColor: '#fff',
              borderWidth: 2
            },
            label: {
              normal: {
                show: false,
                position: 'center',
                formatter(data) { // 设置圆饼图中间文字排版
                  let name = ''
                  if (data.name.length > 5) {
                    name = `${data.name.substr(0, 4)}...`
                  } else {
                    name = data.name
                  }
                  // console.log(name)
                  return `${name}\n${data.value}`
                }
              },
              emphasis: {
                show: true, // 文字至于中间时，这里需为true
                textStyle: { // 设置文字样式
                  fontSize: '16',
                  color: '#0061ce',
                  lineHeight: 24
                }
              }
            },
            labelLine: {
              show: false
            },
            data: this.propData
          }
        ]
      }
      this.chart.setOption(this.option)
      // 设置默认选值第一个为高亮
      this.chart.dispatchAction({ type: 'highlight', seriesIndex: 0, dataIndex: 0 })
      this.chart.on('mouseover', (e) => {
        // 当检测到鼠标悬停事件，不等于第一个（自身）时，取消默认选中高亮
        if (e.dataIndex !== 0) {
          this.chart.dispatchAction({
            type: 'downplay',
            seriesIndex: 0,
            dataIndex: 0
          })
        }
        // 高亮显示悬停的那块
        this.chart.dispatchAction({
          type: 'highlight',
          seriesIndex: 1,
          dataIndex: e.dataIndex
        })
      })
      // 检测鼠标移出后显示之前默认高亮的那块
      this.chart.on('mouseout', (e) => {
        this.chart.dispatchAction({
          type: 'highlight',
          seriesIndex: 0,
          dataIndex: 0
        })
      })
    }
  }
}
</script>
<style scoped>
</style>
