import request from '@/utils/request'

// 添加监控段位
export function add(data) {
  return request({
    url: '/api/v1/move-sensor',
    method: 'post',
    data
  })
}

// 修改监控段位
export function update(data) {
  return request({
    url: '/api/v1/move-sensor',
    method: 'put',
    data
  })
}

// DTS主机分页
export function page(data) {
  return request({
    url: '/api/v1/move-sensor/page',
    method: 'get',
    params: data
  })
}

// 删除dts主机
export function del(data) {
  return request({
    url: '/api/v1/move-sensor/delete',
    method: 'post',
    data
  })
}

// dts导出
export function deviceExport(data) {
  return request({
    url: '/api/v1/dts/device/deviceExport',
    method: 'post',
    data,
    responseType: 'blob'
  })
}

// 位移设备树级数据
export function sensorTree() {
  return request({
    url: '/api/v1/move-sensor/tree',
    method: 'get',
  })
}

// 实时趋势
export function realTimeTrend(sensorId) {
  return request({
    url: '/api/v1/move-sensor/liveTrend',
    method: 'get',
    params: { sensorId }
  })
}

// 传感器单点曲线
export function pointCurve(pointId) {
  return request({
    url: '/api/v1/move-sensor/singleLine',
    method: 'get',
    params: { pointId }
  })
}
