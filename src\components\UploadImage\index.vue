<template>
  <div>
    <el-upload
      ref="upload"
      action="#"
      list-type="picture-card"
      :limit="limit"
      :disabled="disabled"
      accept=".jpg, .png, .jpeg"
      :on-exceed="uploadFileExceed"
      :auto-upload="false"
      :file-list="fileList"
      :on-change="change"
    >
      <i
        slot="default"
        class="el-icon-plus"
      />
      <div
        slot="file"
        slot-scope="{file}"
        class="image-box"
      >
        <img
          class="el-upload-list__item-thumbnail image"
          :src="file.url"
          alt
        >
        <span class="el-upload-list__item-actions">
          <span
            class="el-upload-list__item-preview"
            @click="handlePictureCardPreview(file)"
          >
            <i class="el-icon-zoom-in" />
          </span>
          <span
            v-if="!disabled"
            class="el-upload-list__item-delete"
            @click="handleRemove(file)"
          >
            <i class="el-icon-delete" />
          </span>
        </span>
      </div>
    </el-upload>
    <preview-image
      ref="previewImage"
      :show-viewer="showViewer"
      :url-list="urlList"
    />
  </div>
</template>

<script>
import { compressImage } from '@/utils/index'
import PreviewImage from '../PreviewImage/index'

export default {
  name: 'UploadImage',
  components: {
    PreviewImage
  },
  props: {
    limit: {
      type: Number,
      default: undefined
    },
    disabled: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      dialogImageUrl: '',
      dialogVisible: false,
      // disabled: false,
      urlList: [],
      showViewer: false,
      base64List: [],
      fileList: [],
      inx: undefined
    }
  },
  methods: {
    // 删除图片
    handleRemove(file) {
      this.inx = this.fileList.indexOf(file)
      this.fileList = this.fileList.filter((item) => item.uid !== file.uid)
      this.$emit('deleteImage', this.inx)
    },
    // 预览图片
    handlePictureCardPreview(file) {
      this.urlList = []
      this.url = file.url
      this.urlList.push(file.url)
      this.$refs.previewImage.showViewer = true
    },
    // 超过个数限制
    uploadFileExceed() {
      this.$message.warning(`文件个数最多为${this.limit}个，请删除后再上传`)
    },
    // 上传图片
    async change(file, fileList) {
      if (fileList) {
        this.fileList = fileList
      }
      this.urlList = []
      const base64List = []
      let i = 0
      let arr = []
      arr = this.fileList.filter((item) => item.raw)
      this.fileList.forEach((item) => {
        // 预览列表
        this.urlList.push(item.url)
        if (item.raw) {
          const rawFile = item.raw
          const reader = new FileReader()
          reader.readAsDataURL(rawFile)
          reader.onload = (e) => {
          // 压缩图片
            compressImage(e.target.result, 800, (res) => {
              base64List.push(res)
              i++
              if (i === arr.length) {
                this.$emit('postBase64List', base64List)
              }
            })
          }
        }
      })
      if (arr.length === 0) {
        this.$emit('postBase64List', base64List)
      }
    }
  }
}
</script>

<style>
.image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}
.image-box {
  width: 100%;
  height: 100%;
}
</style>
