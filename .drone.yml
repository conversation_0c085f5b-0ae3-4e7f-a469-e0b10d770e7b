kind: pipeline
name: DemoPipeline
type: docker

steps:
- name: restore-cache
  image: drillster/drone-volume-cache
  settings:
    restore: true
    mount:
      - ./.npm-cache
      - ./node_modules
  volumes:
    - name: cache
      path: /cache
- name: build
  image: node:lts
  commands:
  - npm config set cache ./.npm-cache --global
  - npm --registry https://registry.npm.taobao.org install
  - npm run build:prod
  - cp -R dist /home
  - cp Dockerfile /home
  - cp nginx.conf /home
  volumes:
  - name: data
    path: /home
- name: rebuild-cache
  image: drillster/drone-volume-cache
  settings:
    rebuild: true
    mount:
      - ./.npm-cache
      - ./node_modules
  volumes:
    - name: cache
      path: /cache
- name: deploy
  image: appleboy/drone-ssh
  environment:
    SRV_PWD:
      from_secret: srv_pwd
  settings:
    host: **********
    username: vankeytech
    password:
      from_secret: srv_pwd
    envs:
    - srv_pwd
    script:
    - echo =======暂停容器=======
    - docker stop `docker ps -a | grep jinniu-smart-park-web | awk '{print $1}' `
    - echo =======暂停旧容器和镜像=======
    - docker rm -f `docker ps -a | grep jinniu-smart-park-web | awk '{print $1}' `
    - docker rmi `docker images | grep jinniu-smart-park-web | awk '{print $3}' `
    - echo =======开始构建新镜像=======
    - cd /data/drone/jinniu-smart-park-web
    - docker build -t jinniu-smart-park-web:v1 .
    - if [ $? == 0 ]; then echo "构建成功"; else echo "构建失败"; exit 1; fi
    - echo =======开始部署应用=======
    - docker run -d -p 48080:80 --name jinniu-smart-park-web jinniu-smart-park-web:v1
    - echo =======清理构建文件=======
    - echo $PASSWORD | sudo -S rm -rf *
    - echo =======部署成功=======
- name: notification
  image: fifsky/drone-wechat-work
  settings:
    url:
      from_secret: webhook_url
    msgtype: markdown
    content: |
      {{if eq .Status "success" }}
      ### 🎉<font color="info">构建成功</font>
      #### {{ .Name }}
      > 分支：<font color="comment">{{ .Branch }}</font>
      > 构建者: <font color="comment">${DRONE_COMMIT_AUTHOR_NAME}</font>
      > 构建说明: [{{ .Message }}](${DRONE_COMMIT_LINK})

      [访问项目](http://**********:48080)
      [查看构建记录]({{ .Link }})
      {{else}}
      ### ❌<font color="warning">构建失败</font>
      #### {{ .Name }}
      > 分支：<font color="comment">{{ .Branch }}</font>
      > 构建者: <font color="comment">${DRONE_COMMIT_AUTHOR_NAME}</font>
      > 构建说明: [{{ .Message }}](${DRONE_COMMIT_LINK})
      > 失败阶段：<font color="warning">${DRONE_FAILED_STAGES}</font>

      [查看构建记录]({{ .Link }})
      {{end}}
  when:
    status:
      - success
      - failure

# 挂载的主机卷，可以映射到docker容器中
volumes:
  # npm构建缓存
- name: cache
  host:
    path: /home/<USER>/cache
# npm构建后与宿主机通信的共享目录
- name: data
  host:
    path: /data/drone/jinniu-smart-park-web
trigger:
  branch:
  - master
# 指定Runner
node:
  server: **********