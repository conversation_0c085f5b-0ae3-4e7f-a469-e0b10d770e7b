<script>
import L from 'leaflet'
import 'leaflet/dist/leaflet.css'
import '@geoman-io/leaflet-geoman-free'
import '@geoman-io/leaflet-geoman-free/dist/leaflet-geoman.css'
import { MAP_BOUNDS, MAP_OPTION } from '@/constants'

let map

const icon = L.icon({
  iconUrl: require('@/assets/circle-icon.svg'),
  iconSize: [20, 20],
  iconAnchor: [10, 10]
})

const option = {
  snappable: true,
  snapDistance: 20,
  snapMiddle: true
}

export default {
  name: 'LocateDialog2',
  props: {
    // 背景图地址
    image: {
      type: String,
      default: 'F2.svg'
    },
    points: {
      type: Array,
      default: () => []
    },
    segments: {
      type: Array,
      default: () => []
    },
    ratio: {
      type: Number,
      default: 1
    }
  },
  data() {
    return {
      line: null,
      // 已经添加过的线段
      existingLines: []
    }
  },
  mounted() {
    this.initMap()
    this.initEditor()
  },
  destroyed() {
    console.log('destroy')
  },
  methods: {
    initMap() {
      map = L.map('leaflet-map', MAP_OPTION)
      // 加载图片图层
      L.imageOverlay(this.image, MAP_BOUNDS).addTo(map)
    },
    initEditor() {
      map.pm.addControls({
        position: 'topleft',
        drawCircleMarker: false,
        cutPolygon: false,
        drawMarker: false,
        drawRectangle: false,
        drawText: false,
        drawCircle: false,
        drawPolygon: false,
        rotateMode: false,
        dragMode: false
      })
      map.pm.setLang('zh')

      // listen to vertexes being added to currently drawn layer (called workingLayer)
      map.on('pm:drawstart', ({ workingLayer }) => {
        workingLayer.on('pm:snapdrag', (e) => {
          const confirmedPoints = e.layer.getLatLngs()
          if (confirmedPoints.length) {
            const { marker } = e
            const latlng = marker.getLatLng()
            const latlngs = confirmedPoints.concat(latlng)
            const distance = this.getDistance(latlngs)
            const text = confirmedPoints.length > 1 ? '单击任何存在的标记以完成' : '单击继续绘制'
            marker.setTooltipContent(`
            <div class="distance-tooltip">
              <div>${text}</div>
              <div>${Math.floor(distance * 100) / 100}m</div>
            </div>`)
            marker.openTooltip()
          }
        })
      })

      /**
       * 判断是否已经有光缆数据，没有进入新增模式，有进入编辑模式
       * */
      if (this.points.length) {
        this.drawLine()
      } else {
        // 激活折线绘制
        map.pm.enableDraw('Line', option)
      }
      /** 绘制已经添加过的线段*/
      for (const item of this.segments) {
        const line = L.polyline(item, { color: 'green', weight: 5 }).addTo(map)
        line.pm.disable()
        this.existingLines.push(line)
      }

      map.on('pm:remove', (e) => {
        console.log('pm:remove')
        if (this.existingLines.includes(e.layer)) {
          e.layer.addTo(map)
        } else {
          map.pm.Toolbar.setButtonDisabled('drawPolyline', false)
          // 激活折线绘制
          map.pm.enableDraw('Line', option)
        }
      })

      /** 将已绘制的曲线的编辑功能禁用*/
      map.on('pm:globaleditmodetoggled', (e) => {
        if (e.enabled) {
          for (const item of this.existingLines) {
            item.pm.disable()
          }
        }
      })

      map.on('pm:create', (shape) => {
        this.line = shape.marker
        this.showDistance()

        map.pm.Toolbar.setButtonDisabled('drawPolyline', true)
      })
    },

    /**
     * 显示线段长度
     * */
    showDistance() {
      // 显示线段长度
      const distance = this.getDistance(this.line.getLatLngs())
      this.line.bindTooltip(`<div class="distance-tooltip">${Math.floor(distance * 100) / 100}m</div>`, { permanent: true, direction: 'auto' })

      this.line.on('pm:snapdrag', (e) => {
        const distance = this.getDistance(e.target.getLatLngs())
        e.target.setTooltipContent(`<div class="distance-tooltip">${Math.floor(distance * 100) / 100}m</div>`)
        e.target.openTooltip()
      })
    },
    /**
     * 获取线段长度
     * */
    getDistance(latlngs) {
      let distance = 0
      for (let i = 1; i < latlngs.length; i++) {
        distance += map.distance(latlngs[i - 1], latlngs[i]) * this.ratio
      }
      return distance
    },

    drawLine() {
      this.line = L.polyline(this.points).addTo(map)
      map.fitBounds(this.line.getBounds())
      map.pm.Toolbar.setButtonDisabled('drawPolyline', true)
      this.showDistance()
      this.$nextTick(() => {
        map.pm.enableGlobalEditMode(option)
      })
    },
    /**
     * 供外部调用，校验是否绘制完成
     * */
    validate() {
      /** 如果还没完成绘制则返回*/
      if (map.pm.globalDrawModeEnabled() || !this.line) {
        this.$message.warning({
          message: '请先完成绘制'
        })
        return false
      }
      return true
    },

    /**
     * 外部获取点位
     * */
    getPoints() {
      return this.line.getLatLngs()
    }

  }
}
</script>

<template>
  <div id="leaflet-map" />
</template>

<style scoped lang="scss">
#leaflet-map {
  width: 1200px;
  height: 70vh;
}

</style>

<style lang="scss" scoped>
::v-deep .leaflet-tooltip {
  padding: 5px 8px!important;
  background: white;
  border-radius: 4px;
  text-align: center;
}
</style>

