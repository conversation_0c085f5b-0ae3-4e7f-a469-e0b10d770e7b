const piece = 60 // 60份

export const defaultRem = 1920 / piece // 将设计稿分成60份，一份就是1rem
// @ts-ignore
;(function flexible(window, document) {
  const docEl = document.documentElement
  const dpr = window.devicePixelRatio || 1

  // set 1rem = viewWidth / 60
  function setRemUnit() {
    // 最小自适应屏幕宽度，往下就不再自适应
    const minWidth = 100
    const maxWidth = 5400
    if (docEl.clientWidth <= minWidth) {
      docEl.style.fontSize = `${minWidth / piece}px`
      return
    } if (docEl.clientWidth >= maxWidth) {
      docEl.style.fontSize = `${maxWidth / piece}px`
      return
    }

    const rem = docEl.clientWidth / piece
    docEl.style.fontSize = `${rem}px`
  }

  setRemUnit()

  // reset rem unit on page resize
  window.addEventListener('resize', setRemUnit)
  window.addEventListener('pageshow', (e) => {
    if (e.persisted) {
      setRemUnit()
    }
  })

  // detect 0.5px supports
  if (dpr >= 2) {
    const fakeBody = document.createElement('body')
    const testElement = document.createElement('div')
    testElement.style.border = '.5px solid transparent'
    fakeBody.appendChild(testElement)
    docEl.appendChild(fakeBody)
    if (testElement.offsetHeight === 1) {
      docEl.classList.add('hairlines')
    }

    docEl.removeChild(fakeBody)
  }
})(window, document)
