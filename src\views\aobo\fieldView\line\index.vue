<template>
  <div class="threeOut">

    <div
      ref="container"
      class="threeJs_container"
    />

    <transition appear name="fade">
      <div v-if="!threeJsLoaded" class="loading-text">
        {{ Math.floor(threeJsProgress) }}%
      </div>
    </transition>

    <!-- 实时曲线图表数据 -->
    <el-dialog
      title="详情"
      :visible.sync="dialogVisible"
      width="60vw"
      append-to-body
      destroy-on-close
      top="90px"
    >
      <LineCharts
        ref="chartRef"
        v-loading="loading"
      />
    </el-dialog>

    <!-- 历史数据图表数据 -->
    <el-dialog
      v-if="dialogVisibleMarker"
      title="历史数据"
      :visible.sync="dialogVisibleMarker"
      width="fit-content"
      top="90px"
      append-to-body
      destroy-on-close
    >
      <PointDetailChart
        ref="chartRef"
        :data="formData"
        :alarm-level="alarmLevelList[2].levels"
      />
    </el-dialog>

  </div>

</template>

<script>
import { useRafInterval } from '@/utils/useRafInterval'
import { debounce } from '@/utils'
import LineCharts from '@/views/aobo/curveView/modules/lineCharts'
import PointDetailChart from '@/views/aobo/fieldView/components/PointDetailChart'
import { getAlarmLevelList } from '@/api/alarmTactics'
import {
  camera, Core, css2dRenderer, postProcess, renderer
} from '@/views/aobo/fieldView/threeJs/service/core'
import { RayCasterController } from '@/views/aobo/fieldView/threeJs/service/rayCasterController'
import { mapGetters } from 'vuex'
import Stats from 'three/examples/jsm/libs/stats.module'

let map
export default {
  name: 'LineMap',
  components: { LineCharts, PointDetailChart },
  props: {
    data: {
      type: Object,
      default: () => ({})
    },
    threshold: {
      type: Number,
      default: 0
    },
    interval: {
      type: Number,
      default: 10000
    }
  },

  data() {
    return {
      core: new Core(),
      rayCasterController: new RayCasterController(camera),
      debouncedResizeHandler: null,

      polylineList: [],
      markerList: [],
      cancelTimer: null,
      // 请求数据的时刻
      time: new Date(),
      dialogVisible: false,
      dialogVisibleMarker: false,
      formData: null,
      markerDetail: [],
      loading: false,
      colorList: [
        { color1: '#5470c6', color2: 'rgba(84, 112, 198, 0)' },
        { color1: '#91cc75', color2: 'rgba(145, 204, 117, 0)' },
        { color1: '#fac858', color2: 'rgba(250, 200, 88, 0)' },
        { color1: '#ee6666', color2: 'rgba(238, 102, 102, 0)' },
        { color1: '#73c0de', color2: 'rgba(115, 192, 222, 0)' },
        { color1: '#3ba272', color2: 'rgba(59, 162, 114, 0)' },
        { color1: '#fc8452', color2: 'rgba(252, 132, 82, 0)' },
        { color1: '#9a60b4', color2: 'rgba(154, 96, 180, 0)' },
        { color1: '#ea7ccc', color2: 'rgba(234, 124, 204, 0)' }
      ],
      popup: null,
      updateTime: null,
      classMap: {
        0: 'blue',
        1: 'orange',
        2: 'yellow',
        3: 'red',
      },
      alarmLevelList: []

    }
  },
  computed: {
    ...mapGetters([
      'threeJsLoaded', 'threeJsProgress'
    ])
  },

  watch: {
    interval() {
      this.cancelTimer()
    }
  },
  created() {
    this.getAlarmLevelList()
  },
  mounted() {
    const containerRef = this.$refs.container
    containerRef.appendChild(renderer.domElement)
    containerRef.appendChild(css2dRenderer.domElement)
    this.onResize()

    const stats = new Stats()
    const statsDom = stats.domElement
    statsDom.classList.add('stats')
    document.body.appendChild(statsDom)

    const updateState = () => {
      stats.update()

      requestAnimationFrame(() => {
        updateState()
      })
    }

    requestAnimationFrame(updateState)
  },
  beforeDestroy() {
    this.core.dispose()
    window.removeEventListener('resize', this.debouncedResizeHandler)
    this.removeCabinetGroup()
    // postProcess.outlinePass.selectedObjects = []
  },

  destroyed() {
    this.cancelTimer()
    // 清除之前的线段
    for (const item of this.polylineList) {
      item.remove()
    }
    this.polylineList = []

    // 清除之前的marker
    for (const item of this.markerList) {
      item.remove()
    }
    this.markerList = []
  },
  methods: {
    // 窗口大小变化后重新设置threeJs画布尺寸
    onResize() {
      const containerRef = this.$refs.container
      const resizeHandler = () => {
        const { width, height } = containerRef.getBoundingClientRect()
        this.core.changeSize(width, height)
      }
      resizeHandler()
      this.debouncedResizeHandler = debounce(resizeHandler, 100, false)
      window.addEventListener('resize', this.debouncedResizeHandler)
    },

    /**
     * 获取等级列表
     * */
    getAlarmLevelList() {
      getAlarmLevelList()
        .then((res) => {
          this.alarmLevelList = res.data
        })
    },

    /**
     * 供外部调用，获取当前请求数据的时刻
     */
    getTime() {
      return this.updateTime
    },
    /**
     * 供外部调用，根据serialNumber条转到对应的线段
     * */
    goToLine(serialNumber) {
      for (const item of this.polylineList) {
        if (serialNumber.startsWith(item.data.serialNum)) {
          map.fitBounds(item.getBounds())
          return
        }
      }
    },
    /**
     * 供外部调用，跳转对应marker
     * */
    goToMarker(id) {
      for (const item of this.markerList) {
        if (item.data.pointId === id) {
          map.setView(item.getLatLng(), 18)
          return
        }
      }
    }
  }
}
</script>

<style scoped lang="scss">
.threeOut {
  position: relative;
  height: 100%;
}
.threeJs_container {
  height: 100%;
  position: absolute;
  inset: 0;
  z-index: 9;
}

.loading-text {
  position: absolute;
  inset: 0;
  display: grid;
  place-items: center;
  font-size:  80px;
  z-index: 999;
  user-select: none;
  color: #bdbcbc;
  text-shadow:
    0 1px 0 hsl(174, 5%, 80%),
    0 2px 0 hsl(174, 5%, 75%),
    0 3px 0 hsl(174, 5%, 70%),
    0 4px 0 hsl(174, 5%, 66%),
    0 5px 0 hsl(174, 5%, 64%),
    0 6px 0 hsl(174, 5%, 62%),
    0 7px 0 hsl(174, 5%, 61%),
    0 8px 0 hsl(174, 5%, 60%),
    0 0 5px rgba(0, 0, 0, 0.05),
    0 1px 3px rgba(0, 0, 0, 0.2),
    0 3px 5px rgba(0, 0, 0, 0.2),
    0 5px 10px rgba(0, 0, 0, 0.2),
    0 10px 10px rgba(0, 0, 0, 0.2),
    0 20px 20px rgba(0, 0, 0, 0.3);
}

</style>

<style lang="scss">
.sensor-pop {
  border-radius: 6px;
  padding: 10px;
  color: white;
  text-align: center;
  // 内阴影
  box-shadow: 0 0 20px 10px #193f84 inset;
  .temperature {
    color: #00E667;
    text-align: center;
    font-weight: bold;
  }

  &.blue {
    box-shadow: 0 0 20px 10px rgba(0, 0, 255, 0.5) inset;
    .temperature {
      color: #4792f6;
    }
  }
  &.orange {
    box-shadow: 0 0 20px 10px rgba(255, 165, 0, 0.5) inset;
    .temperature {
      color: #FFA500;
    }
  }

  &.yellow {
    box-shadow: 0 0 20px 10px rgba(255, 255, 0, 0.5) inset;
    .temperature {
      color: #FFFF00;
    }
  }

  &.red {
    box-shadow: 0 0 20px 10px rgba(255, 0, 0, 0.5) inset;
    .temperature {
      color: red;
    }
  }
}

</style>
