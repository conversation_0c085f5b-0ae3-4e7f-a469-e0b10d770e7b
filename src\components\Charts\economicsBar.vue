<template>
  <div
    :id="id"
    :style="{ height: height, width: width }"
  />
</template>
<script>
import chartsMixIn from './mixins'

export default {
  name: 'EconomicsBar',
  mixins: [chartsMixIn],
  props: {
    id: {
      require: false,
      type: String,
      default: 'charts'
    },
    width: {
      require: false,
      type: String,
      default: '100%'
    },
    height: {
      require: false,
      type: String,
      default: '100%'
    },
    propData: {
      require: false,
      type: Object,
      default: () => {}
    }
  },
  created() {
    this.doNotRedraw = true
  },
  mounted() {
    this.initChart()
    // console.log(this.propData, 888888888)
  },
  methods: {
    initChart() {
      this.chart = this.$echarts.init(document.getElementById(this.id))
      this.option = {
        color: ['#4694EC', '#56E2DD', '#EFCB8D'],
        title: {
          text: '企业经济变化',
          textStyle: {
            color: 'rgb(0, 0, 0)'
          }
        },
        tooltip: {
          trigger: 'axis'
        },
        legend: {
          data: this.propData.XData,
          icon: 'circle',
          right: '8%'
        },
        xAxis: [
          {
            type: 'category',
            data: this.propData.timeStr
          }
        ],
        yAxis: [
          {
            type: 'value',
            name: '（万元）',
            splitLine: {
              lineStyle: {
                type: 'dashed',
                color: '#E9E9E9'
              }
            }
          }
        ],
        series: [
          {
            name: '税收',
            type: 'bar',
            barWidth: 16, // 柱图宽度
            data: this.propData.taxTotal
          },
          {
            name: '产值',
            type: 'bar',
            barWidth: 16, // 柱图宽度
            data: this.propData.outputValueTotal
          },
          {
            name: '营收',
            type: 'bar',
            barWidth: 16, // 柱图宽度
            data: this.propData.revenueTotal
          }
        ]
      }
      this.chart.setOption(this.option)
      // this.autoPlayTool(this.chart, this.propData, 0)
    }
  }
}
</script>
<style scoped>
</style>
