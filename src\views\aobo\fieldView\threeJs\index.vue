<template>
  <div ref="container" class="threeJs_container" />
</template>

<script>
import {
  camera,
  Core,
  CSS2D_CONTAINER_ID,
  css2dRenderer,
  environment,
  orbitControls,
  postProcess,
  renderer,
  scene,
} from '@/views/aobo/fieldView/threeJs/service/core'
import { debounce, deepClone } from '@/utils'
import * as THREE from 'three'
import { CSS2DObject } from 'three/examples/jsm/renderers/CSS2DRenderer'
import Vue from 'vue'
import gsap from 'gsap'
import { RayCasterController } from '@/views/aobo/fieldView/threeJs/service/rayCasterController'
import { mapGetters } from 'vuex'
import PopupComponent from '../components/popup.vue'
import PopupComponentHorizontal from '../components/popupHorizontal.vue'

const camaraPos = [32.82635021658241, 58.14891945680124, -436.7358164425371]
const targetPos = [0.10056876533252802, -21.343487218980897, 154.10340504744244]

export default {
  name: 'FieldViewThreeJs',
  props: {
    threshold: Number,
  },
  data() {
    return {
      core: new Core(),
      rayCasterController: new RayCasterController(camera),
      debouncedResizeHandler: null,
      // 模型组，用于存放所有机柜和弹窗
      modelGroup: new THREE.Group(),
      // 存放射线拾取的对象
      raycastObjects: [],
      // 二级页面模型
      secondPageModel: null,
      // 二级二面弹窗组
      secondPagePopupGroup: new THREE.Group(),
      // 模型尺寸
      size: null,
      firstPageCameraPos: null,
      firstPageTargetPos: null,
      flag: false, // 是否有过渡动画
    }
  },
  computed: {
    ...mapGetters(['threeJsLoaded']),
  },
  created() {
    postProcess.init()
  },
  mounted() {
    const containerRef = this.$refs.container
    containerRef.appendChild(renderer.domElement)
    containerRef.appendChild(css2dRenderer.domElement)
    this.onResize()
  },
  beforeDestroy() {
    this.core.dispose()
    window.removeEventListener('resize', this.debouncedResizeHandler)
    this.removeCabinetGroup()
    postProcess.outlinePass.selectedObjects = []
  },
  methods: {
    // 窗口大小变化后重新设置threeJs画布尺寸
    onResize() {
      const containerRef = this.$refs.container
      const resizeHandler = () => {
        const { width, height } = containerRef.getBoundingClientRect()
        this.core.changeSize(width, height)
      }
      resizeHandler()
      this.debouncedResizeHandler = debounce(resizeHandler, 100, false)
      window.addEventListener('resize', this.debouncedResizeHandler)
    },
    /**
     * 添加一级页面的模型
     * */
    goFirstPage(list, updateCamera = false) {
      if (!this.threeJsLoaded) return
      const modelScene = environment.model.scene
      // 获取模型的尺寸
      const size = environment.modelSize
      // 创建模型组
      const { modelGroup } = this
      modelGroup.clear()
      scene.add(this.modelGroup)
      this.modelGroup.clear()
      // 将模型选转180度
      // modelGroup.rotation.y = Math.PI

      // 将list按照gridRow排列生成模型
      for (let i = 0; i < list.length; i++) {
        const cloned = modelScene.clone()
        const item = deepClone(list[i])
        cloned.userData = { data: item }
        // 通过行、列确定电池柜的位置
        const { tierRow: row, tierColumn: col } = item
        if (!row) {
          this.$message.warning({ message: '电池柜位置信息不全，请在电池柜管理配置位置信息' })
          continue
        }
        cloned.position.set(col * size.x, 47.8, row * size.z)
        // cloned.position.set(Math.floor(i / gridRow) * 100, 47.8, (i % gridRow) * size.z)
        const popup = this.addPopup(PopupComponent, cloned.position, item, this.goSecondPage)
        modelGroup.add(popup)
        modelGroup.add(cloned)
        cloned.traverse((child) => {
          if (child instanceof THREE.Mesh) {
            child.userData = { data: item }
            // child.material = new THREE.MeshBasicMaterial({ color: new THREE.Color(randomColor()) })
          }
        })
        this.raycastObjects.push(cloned)
      }

      /**
       * 通过包围盒计算模型中心点，需要等动画结束
       */
      if (updateCamera || !this.firstPageCameraPos) {
        setTimeout(
          () => {
            this.getCenterPosition(modelGroup)
          },
          this.flag ? 1000 : 0
        )
      }
      this.flag = false

      postProcess.outlinePass.selectedObjects = this.raycastObjects
      // 添加射线拾取
      this.rayCasterController.bindClickRayCastObj([...this.raycastObjects], (obj) => {
        this.goSecondPage(obj.userData.data)
      })
    },
    /**
     * 通过包围盒计算模型中心点，需要等动画结束
     */
    getCenterPosition(modelGroup) {
      const box3 = new THREE.Box3()
      // 计算层级模型group的包围盒
      // 模型group是加载一个三维模型返回的对象，包含多个网格模型
      box3.expandByObject(modelGroup)
      // 计算一个层级模型对应包围盒的几何体中心在世界坐标中的位置
      const center = new THREE.Vector3()
      box3.getCenter(center)

      const cameraPosition = center
      camera.lookAt(cameraPosition)
      orbitControls.target.copy(cameraPosition)
      // 获取相机位置
      this.firstPageCameraPos = [...camera.position]
      this.firstPageTargetPos = [...cameraPosition]
    },

    /**
     * 添加弹窗
     * */
    addPopup(component, position, data, onMousedown) {
      const CompConstructor = Vue.extend(component)
      const div = document.createElement('div')
      const instance = new CompConstructor({ el: div, propsData: { data, threshold: this.threshold }})
      const popupEl = instance.$el
      const popup = new CSS2DObject(popupEl)
      const popupPosition = position.clone().add(new THREE.Vector3(0, 10, 0))
      popup.position.copy(popupPosition)
      // 给弹窗注册点击事件
      popupEl.addEventListener('mousedown', (e) => {
        // 仅针对鼠标左键点击
        if (e.button !== 0) return
        e.stopPropagation()
        onMousedown(data)
      })
      return popup
    },
    /**
     * 移除所有机柜和弹窗
     * */
    removeCabinetGroup() {
      scene.remove(this.modelGroup)
      this.removePopup()
    },
    /**
     * 移除弹窗
     * */
    removePopup() {
      const css2dContainer = document.getElementById(CSS2D_CONTAINER_ID)
      css2dContainer.innerHTML = ''
    },
    /**
     * 二级页面：查看单个机柜详情
     * */
    goSecondPage(data) {
      this.removeFirstPageModel()
      const singleModel = environment.singleCabinetModel.clone()
      // const singleModel = environment.model.scene.clone()
      this.secondPageModel = new THREE.Group()
      this.secondPageModel.add(singleModel)
      const modelScene = this.secondPageModel
      postProcess.outlinePass.selectedObjects = [this.secondPageModel]
      postProcess.outlinePass.edgeStrength = 6 // 包围线浓度
      postProcess.outlinePass.edgeGlow = 5 // 边缘线范围
      postProcess.outlinePass.edgeThickness = 12 // 边缘线浓度
      postProcess.outlinePass.pulsePeriod = 5 // 包围线闪烁频率

      scene.add(this.secondPagePopupGroup)
      scene.add(modelScene)

      // 相机移动动画
      gsap.to(camera.position, {
        x: camaraPos[0],
        y: camaraPos[1],
        z: camaraPos[2],
        duration: 1,
      })

      gsap.to(orbitControls.target, {
        x: targetPos[0],
        y: targetPos[1],
        z: targetPos[2],
        duration: 1,
      })
      this.$emit('detail', data)
    },

    /**
     * 添加二级页面详情弹窗
     * */
    addSecondPopup(list) {
      this.addBatteryCabinetUpwards(list)
      this.secondPagePopupGroup.clear()
      this.removePopup()
      // 添加层级弹窗
      for (let i = 0; i < list.length; i++) {
        const position = this.secondPageModel.position.clone().add(new THREE.Vector3(0, 30 * i + 30, 0))
        const popup = this.addPopup(PopupComponentHorizontal, position, list[i], this.goThirdPage)
        this.secondPagePopupGroup.add(popup)
      }
    },
    /**
     * 向上追加电池柜
     * **/
    addBatteryCabinetUpwards(list) {
      const listLength = Math.ceil(list.length / 1)
      const modelLength = this.secondPageModel.children.length
      // 如果数据长度大于模型长度，向上添加模型
      if (listLength > modelLength) {
        const topModel = this.secondPageModel.children.at(-1)
        // 获取模型的尺寸
        const box = new THREE.Box3().setFromObject(topModel)
        const modelSize = box.getSize(new THREE.Vector3())

        for (let i = 1; i < listLength; i++) {
          const position = topModel.position.clone().add(new THREE.Vector3(0, modelSize.y * i, 0))
          const singleModel = topModel.clone()
          this.secondPageModel.add(singleModel)
          /** 添加模型从上往下盖下来的动画*/
          // 定义模型初始位置
          const start = { x: position.x, y: 500, z: position.z }
          singleModel.position.set(start)
          gsap.to(start, {
            x: position.x,
            y: position.y,
            z: position.z,
            ease: 'power2.out',
            duration: 0.5,
            onUpdate: () => {
              singleModel.position.set(start.x, start.y, start.z)
            },
          })
        }
      }
    },
    /**
     * 移除一级页面模型
     * */
    removeFirstPageModel() {
      // 移除射线拾取（关闭监听模型点击事件）
      this.rayCasterController.dispose()
      this.raycastObjects = []
      // 移除一级页面中的模型
      this.removeCabinetGroup()
    },
    /**
     * 从二级页面进入三级页面
     * */
    goThirdPage(data) {
      // 移除二级页面中的模型
      scene.remove(this.secondPagePopupGroup)
      scene.remove(this.secondPageModel)
      this.removePopup()
      this.$emit('showLayerDetail', data)
    },
    /**
     * 供外部组件调用：从二级页面返回主页
     * */
    backFirstPage(transition = true) {
      this.flag = true
      scene.remove(this.secondPagePopupGroup)
      this.removePopup()
      this.secondPagePopupGroup.clear()
      scene.remove(this.secondPageModel)
      postProcess.outlinePass.selectedObjects = this.raycastObjects
      postProcess.resetOutlinePass()

      scene.add(this.modelGroup)
      this.rayCasterController.restart()
      if (transition) {
        const camaraPos = this.firstPageCameraPos
        gsap.to(camera.position, {
          x: camaraPos[0],
          y: camaraPos[1],
          z: camaraPos[2],
          duration: 1,
        })

        const targetPos = this.firstPageTargetPos
        gsap.to(orbitControls.target, {
          x: targetPos[0],
          y: targetPos[1],
          z: targetPos[2],
          duration: 1,
        })
      }
    },
    /**
     * 供外部组件调用：从三级页面返回二级页面
     * */
    backSecondPage() {
      camera.position.set(-100.00159623104264, 140.8824767314175, 161.45122335761926)
      orbitControls.target.set(-370.6269433938471, -54.97037027163725, -88.0491789736449)

      setTimeout(() => {
        // const modelScene = environment.singleCabinetModel.clone()
        const modelScene = this.secondPageModel

        scene.add(this.secondPagePopupGroup)
        scene.add(modelScene)
        // 相机移动动画
        gsap.to(camera.position, {
          x: camaraPos[0],
          y: camaraPos[1],
          z: camaraPos[2],
          duration: 1,
        })

        gsap.to(orbitControls.target, {
          x: targetPos[0],
          y: targetPos[1],
          z: targetPos[2],
          duration: 1,
        })
      }, 40)
    },
  },
}
</script>

<style scoped lang="scss">
.threeJs_container {
  position: absolute;
  inset: 0;
  z-index: 9;
}
</style>
