<template>
  <div
    v-loading="loading"
    class="main-content"
  >
    <div class="top">
      <div style="display: flex;align-items:center">
        <el-cascader
          v-model="position"
          :options="dropdownList"
          :props="{value: 'id', children: 'childList', label: 'name', emitPath: false }"
          @change="positionChange"
        />
        <div style="margin-left:50px;font-weight:bold;">数据刷新间隔时间:</div>
        <el-select
          v-model="timeInterval"
          placeholder="请选择"
          filterable
          allow-create
          style="width:100px;margin:0 10px"
          @change="timeIntervalChange"
        >
          <el-option
            v-for="item in timeList"
            :key="item"
            :label="item"
            :value="item"
          />
        </el-select>
        <div style="font-weight:bold;">秒</div>
      </div>
      <div style="display:flex;align-items: center">
        <el-button style="margin-right: 20px;" type="primary" @click="toHead">首部</el-button>
        <el-button style="margin-right: 20px;" type="danger" @click="toEnd">尾部</el-button>
        <el-button style="margin-right: 20px;" type="primary" @click="openExportDialog">导出</el-button>
        <div style="fon-size:15px;font-weight:bold;margin-right:20px;">
          {{ nowTime }}
        </div>
        <img
          style="width:20px;height:20px;cursor:pointer;"
          src="@/assets/page/quanping.png"
          @click="handleFullScreen"
        >
      </div>
    </div>
    <div
      id="content"
      class="content"
      :class="isFullscreen ? 'full_content' : 'normal_content'"
      style="background-size: 100% 100% !important;background-repeat: no-repeat;"
    >
      <div
        v-if="showFullAlarm"
        id="full-alarm"
        class="full_alarm"
      >
        <div />
      </div>
      <LineMap
        ref="lineMapRef"
        :threshold="threshold"
        :data="detailData"
        :interval="timeInterval"
      />
      <div class="left_top">
        <div class="room_title">
          <img
            src="@/assets/page/<EMAIL>"
            class="img1"
          >
          <div
            style="font-size: 27px;font-weight:500;cursor: pointer;z-index:999;font-family:Title;"
          >{{ detailData.name }}</div>
        </div>
        <div
          v-if="deviceList && deviceList.length"
          class="time"
        >
          <img src="@/assets/page/<EMAIL>">
          <div>{{ `当前数据采集时间：${$refs.lineMapRef && $refs.lineMapRef.getTime() || '---'}` }}</div>
        </div>
        <div
          v-if="deviceList && deviceList.length"
          class="info_box"
        >
          <div
            class="base_info"
          >
            <template>
              <div
                v-for="item in deviceList"
                :key="item.id"
                style="margin-bottom:18px"
              >
                <div class="base_info_item">
                  <span style="color:#C4DDFF;">DTS主机信息：</span>
                  <span style="color:#FFFFFF;">
                    {{ item.name || '--' }}
                  </span>
                </div>
                <div class="base_info_item">
                  <span style="color:#C4DDFF;">刷新时间：</span>
                  <span style="color:#FFFFFF;">
                    {{ item.refreshInterval || item.refreshInterval === 0 ? `${item.refreshInterval}s` : '--' }}
                  </span>
                </div>
                <div
                  class="base_info_item"
                  style="margin-bottom:3px"
                >
                  <span style="color:#C4DDFF;">光缆信息：</span>
                </div>
                <template
                  v-if="item.cableList && item.cableList.length"
                >
                  <div
                    v-for="item1 in item.cableList"
                    :key="item1.id"
                    style="margin-bottom:10px;margin-left:5px"
                  >
                    <div class="base_info_item">
                      <span style="color:#FFFFFF;">{{ item1.name || '--' }}</span>
                    </div>
                    <div class="base_info_item">
                      <span style="color:#C4DDFF;">长度：</span>
                      <span style="color:#FFFFFF;">
                        {{ item1.cableLength || item1.cableLength === 0 ? `${item1.cableLength / 100}m` : '--' }}
                      </span>
                    </div>
                    <div class="base_info_item">
                      <span style="color:#C4DDFF;">纤芯类型：</span>
                      <span style="color:#FFFFFF;">
                        {{ item1.fibreCoreSize || '--' }}
                      </span>
                    </div>
                  </div>
                </template>
              </div>
            </template>
          </div>
        </div>
      </div>

      <img
        v-if="isFullscreen"
        src="@/assets/page/tuichu.png"
        title="退出全屏"
        class="out_fullscreen"
        @click="handleFullScreen"
      >
      <AlarmBattery
        ref="alarmBattery"
        :is-fullscreen="isFullscreen"
        :btn-authority-list="btnAuthorityList"
        :time-interval="timeInterval"
        :serial-num="detailData.serialNum"
        :work-face-id="position"
        @detail="toAlarmLine"
        @sensorDetail="sensorDetail"
      />
    </div>

    <!-- 导出弹窗 -->
    <el-dialog
      v-if="exportDialogVisible"
      title="数据导出"
      :visible.sync="exportDialogVisible"
      width="fit-content"
      top="90px"
      append-to-body
      destroy-on-close
    >

      <el-form
        ref="formRef"
        :model="formData"
        :rules="rules"
        label-width="120px"
        style="padding: 0 50px"
      >

        <el-form-item
          label="数据类型:"
          prop="name"
        >
          <el-select
            v-model="formData.dataType"
            placeholder="请选择"
            filterable
            allow-create
            style="width:360px;"
            @change="dataTypeChange"
          >
            <el-option
              v-for="(label, value) in dataTypeMap"
              :key="value"
              :label="label"
              :value="Number(value)"
            />
          </el-select>
        </el-form-item>

        <!--        光缆-->
        <div v-if="formData.dataType === 1">
          <el-form-item
            label="监控段:"
            prop="id"
          >
            <el-cascader
              ref="cascaderRef"
              v-model="formData.id"
              style="width:360px;"
              :options="fiberTree"
              collapse-tags
              :clearable="false"
              :props="{ value: 'id', label: 'name', children: 'childList',multiple: false, emitPath: false }"
              @change="segmentChange"
            />
          </el-form-item>
          <el-form-item
            label="监控段范围"
          >
            <div style="width:360px;display: flex;">

              <el-input-number
                v-model="min"
                :min="0"
                :max="max"
                :controls="false"
              />
              <span style="margin: 0 10px">~</span>
              <el-input-number
                v-model="max"
                :min="min"
                :max="maxValue"
                :controls="false"
              />
              <span style="margin-left: 10px;">米</span>
            </div>
          </el-form-item>
        </div>

        <!--        位移传感器-->
        <div v-else>
          <el-form-item
            label="位移传感器:"
            prop="sensorIds"
          >

            <el-cascader
              v-model="formData.sensorIds"
              style="width:360px;"
              :options="sensorTree"
              collapse-tags
              clearable
              :props="{ value: 'id', label: 'name', children: 'childList',multiple: true, emitPath: false }"
            />

          </el-form-item>
        </div>

        <el-form-item
          label="时间范围:"
          prop="dateRange"
        >
          <el-date-picker
            v-model="formData.dateRange"
            type="daterange"
            placeholder="选择日期"
            :clearable="false"
            value-format="yyyy-MM-dd"
            format="yyyy-MM-dd"
            append-to-body
            style="width:360px;"
            :picker-options="{disabledDate}"
          />
        </el-form-item>

      </el-form>

      <div
        slot="footer"
        class="dialog_footer"
      >
        <el-button
          type="primary"
          plain
          style="margin-right:10px"
          @click="exportDialogVisible = false"
        >取 消
        </el-button>
        <el-button
          :loading="exporting"
          type="primary"
          @click="handleExport"
        >确 定
        </el-button>
      </div>
    </el-dialog>

  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import dayjs from 'dayjs'
import { dtsAlarmThreshold, sensorTree } from '@/api/lineCharts'
import Cookies from 'js-cookie'
import { playAudio } from '@/utils/palyAudio'
import {
  deepClone, flatData, useDebounceFn, utf8to16
} from '@/utils'
import AlarmBattery from '@/views/aobo/fieldView/components/alarmList/index'
import store from '@/store'
import {
  ExportDisplacementHistory,
  ExportTemperatureHistory,
  getCabinet,
  getPointTree,
  getWorkFaceRelation,
  spaceTree
} from '@/api/deviceManage'
import { camera, orbitControls } from '@/views/aobo/fieldView/threeJs/service/core'
import gsap from 'gsap'
import * as THREE from 'three'
import LineMap from './line/index'

/**
 * 获取树级数据的第一条的最后一级数据
 * */
function getFirstData(data) {
  if (!data.childList || !data.childList.length) return data
  return getFirstData(data.childList[0])
}

// 报警持续闪烁时间（s）
const ALARM_TIME = 10
export default {
  name: 'FieldView',
  components: {
    LineMap,
    AlarmBattery
  },
  data() {
    return {
      btnAuthorityList: [], // 按钮权限
      nowTime: null,
      detailData: {},
      position: null, // 煤矿位置
      dropdownList: [], // 煤矿下拉框
      flatList: [],
      timeList: [10, 30, 60], // 间隔时间下拉框
      timeInterval: Number(localStorage.getItem('timeInterval')) || 60,
      threshold: 0, // 阈值
      intervalNow: null, // 当前数据采集时间
      isFullscreen: false, // 是否全屏
      serialNum: null, // 序列号
      showFullAlarm: false, // 是否展示全屏告警
      socket: null, // WebSocket
      socketCode: 1, // 状态码
      closeAudio: null,
      loading: false,
      timer: null, // 显示全屏报警定时器
      deviceList: [],
      exportDialogVisible: false,
      formData: null, // 导出表单信息
      rules: {
        dateRange: [{ required: true, message: '请选择时间范围', trigger: 'blur' }],
        id: [{ required: true, message: '请选择', trigger: 'blur' }],
        sensorIds: [{ required: true, message: '请选择', trigger: 'blur' }],
      },
      exporting: false,
      dataTypeMap: {
        1: '温度',
        2: '位移传感器'
      },
      fiberTree: [],
      min: 0,
      max: 1,
      maxValue: 100,
      sensorTree: [],
      currentSegment: null
    }
  },
  computed: {
    ...mapGetters([
      'threeJsLoaded', 'btnAuthority', 'threeJsProgress'
    ])
  },
  created() {
    this.areaDropdown()
    const cur = this.btnAuthority.find((item) => item.code === this.$route.meta.code)
    if (cur) this.btnAuthorityList = cur.functionPermissionCode
    this.fiberDropdown()
    this.sensorDropdown()
  },
  mounted() {
    this.websocketConnect()
    this.dtsAlarmThreshold()
    this.nowTime = dayjs().format('YYYY年MM月DD日 HH:mm:ss')
    this.intervalNow = setInterval(() => {
      this.nowTime = dayjs().format('YYYY年MM月DD日 HH:mm:ss')
    }, 1000)
  },
  beforeDestroy() {
    store.dispatch('threeJs/setLoaded', false)
    this.socket.close()
    this.socket = null
    this.closeAudio?.()
    clearInterval(this.intervalNow)
  },
  methods: {
    websocketConnect() {
      // 石伟
      this.socket = new WebSocket('ws://192.168.0.62:21021/ws')

      // 测试服
      // this.socket = new WebSocket('wss://aobo-dts-test.vankeytech.com:9902/ws')
      // 线上
      // this.socket = new WebSocket('ws://10.108.183.106/ws')
      // 监听socket连接
      this.socket.onopen = () => {
        const data = {
          code: this.socketCode,
          token: Cookies.get('governance_token')
        }
        this.socket.send(JSON.stringify(data))
      }
      // 监听socket错误信息
      this.socket.onerror = (err) => {
        console.log(err, 'err')
      }
      // 消息防抖处理
      const debouncedFn = useDebounceFn((msg) => {
        const res = JSON.parse(msg.data)
        if (res.code === 200 && this.socketCode === 1) {
          this.socketCode++
          const data = {
            code: this.socketCode,
            msgId: res.message
          }
          this.socket.send(JSON.stringify(data))
        } else if (res.code === 200 && res.message) {
          const { data } = res
          this.showFullAlarm = true
          const type = data.alarmType === 2 ? '2' : '1'
          this.$refs.alarmBattery?.doPage(type)
          if (data.isEnableAudio) {
            this.closeAudio?.()
            this.closeAudio = playAudio(data.audioUrl, data.cycleCount)
          }
          clearTimeout(this.timer)
          this.timer = setTimeout(() => {
            this.showFullAlarm = false
          }, ALARM_TIME * 1000)
        }
      }, 1000)
      // 监听socket消息
      this.socket.onmessage = debouncedFn
      this.socket.onclose = () => {
        if (this.$route.meta.code !== 'XCST') return
        this.socketCode = 1
        this.socket = null
        this.websocketConnect()
      }
    },
    fiberDropdown() {
      spaceTree().then((res) => {
        for (const item of res.data) {
          if (!item.childList?.length) {
            item.disabled = true
          } else {
            // 判断子级是否全被禁用
            let allDisabled = true
            for (const item1 of item.childList) {
              if (!item1.childList?.length) {
                item1.disabled = true
              } else {
                allDisabled = false
              }
            }

            // 若子级全被禁用父级也需被禁用
            item.disabled = allDisabled
          }
        }
        this.fiberTree = res.data
      })
    },
    sensorDropdown() {
      sensorTree().then((res) => {
        for (const item of res.data) {
          if (!item.childList?.length) {
            item.disabled = true
          } else {
            // 判断子级是否全被禁用
            let allDisabled = true
            for (const item1 of item.childList) {
              if (!item1.childList?.length) {
                item1.disabled = true
              } else {
                allDisabled = false
              }
            }

            // 若子级全被禁用父级也需被禁用
            item.disabled = allDisabled
          }
        }
        this.sensorTree = res.data
      })
    },

    // 选择数据刷新间隔时间
    timeIntervalChange() {
      localStorage.setItem('timeInterval', this.timeInterval)
    },
    handleFullScreen() {
      this.isFullscreen = !this.isFullscreen
      const myEvent = new Event('resize')
      window.dispatchEvent(myEvent)
    },
    // 获取报警阈值
    dtsAlarmThreshold() {
      dtsAlarmThreshold().then((res) => {
        this.threshold = res.data?.threshold
      })
    },
    // 区域下拉框
    areaDropdown() {
      this.loading = true
      getPointTree().then((res) => {
        this.dropdownList = res.data
        this.flatList = flatData(res.data)
        const data = deepClone(getFirstData(this.dropdownList[0]))
        this.position = data?.id
        this.positionChange()
        this.serialNum = data?.serialNum
      }).finally(() => (this.loading = false))
    },
    positionChange() {
      const cur = this.flatList.find((item) => item.id === this.position)
      if (cur.spaceType !== 2) {
        this.$message.warning('请选择工作面')
        return
      }
      this.loading = true
      getCabinet(this.position).then((res) => {
        this.detailData = {}
        this.$nextTick(() => {
          this.detailData = res.data
          this.serialNum = this.detailData.serialNum
        })
      }).finally(() => {
        this.loading = false
      })
      getWorkFaceRelation(this.position).then((res) => {
        this.deviceList = res.data
      })
    },

    /**
     * 跳转到告警监控段
     * */
    toAlarmLine(row) {
      this.$refs.lineMapRef.goToLine(row.spaceSerialNum)
    },
    sensorDetail(row) {
      this.$refs.lineMapRef.goToMarker(row.pointId)
    },

    /**
     * 导出
     * */
    openExportDialog() {
      this.formData = { dataType: 1, dateRange: [] }
      this.exportDialogVisible = true
    },
    disabledDate(time) {
      return time.getTime() > new Date().getTime()
    },
    dataTypeChange() {
      // this.formData.id = null
      // this.formData.dateRange = []
    },
    handleExport() {
      this.$refs.formRef.validate().then(() => {
        this.exporting = true
        const query = { startDate: this.formData.dateRange[0], endDate: this.formData.dateRange[1] }
        if (this.formData.dataType === 1) {
          query.startPosition = this.min * 100 + this.currentSegment.startPosition
          query.endPosition = this.max * 100 + this.currentSegment.startPosition
          query.segmentId = this.formData.id
        } else {
          query.pointIds = this.formData.sensorIds.join(',')
        }

        const fn = this.formData.dataType === 1 ? ExportTemperatureHistory : ExportDisplacementHistory
        fn(query).then((res) => {
          const url = window.URL.createObjectURL(
            new Blob([res.data], {
              type: 'application/vnd.ms-excel;charset=UTF-8'
            })
          )
          const temp = res.headers['content-disposition']
            .split(';')[1]
            .split('filename=')[1]
          const index = temp.indexOf('.')
          const str = temp.substr(0, index)
          const fileName = `${utf8to16(unescape(str))}.csv`
          const link = document.createElement('a')
          link.href = url
          link.download = fileName
          link.click()
          this.$message({
            type: 'success',
            message: '导出成功'
          })
          this.exportDialogVisible = false
        }).finally(() => {
          this.exporting = false
        })
      })
    },
    segmentChange(e) {
      const { data } = this.$refs.cascaderRef.getCheckedNodes()[0]
      this.currentSegment = data
      this.maxValue = (data.endPosition - data.startPosition) / 100
      console.log(data)
      this.min = 0
      this.max = this.maxValue
    },
    toHead() {
      if (!this.threeJsLoaded) {
        this.$message.warning('模型加载中')
        return
      }

      const position = new THREE.Vector3(-538, -26, -6)
      orbitControls.target.copy(position)
      orbitControls.update()
      gsap.to(camera.position, {
        x: -611,
        y: -13,
        z: -42,
        duration: 1,
        ease: 'power2.inOut',
      })
    },
    toEnd() {
      if (!this.threeJsLoaded) {
        this.$message.warning('模型加载中')
        return
      }

      const position = new THREE.Vector3(539, -8, -86.8)
      orbitControls.target.copy(position)
      orbitControls.update()
      gsap.to(camera.position, {
        x: 573,
        y: 28,
        z: -191,
        duration: 1,
        ease: 'power2.inOut',
      })
    }

  }
}
</script>

<style lang="scss" scoped>
@keyframes blink {
  0% {
    opacity: 0;
  }
  50% {
    opacity: 0.5;
  }
  100% {
    opacity: 0;
  }
}
.main-content{
  //height: calc(100%);
  padding-top: 30px;
  box-sizing: border-box;
  font-family: PingFang SC RE;
  .top {
    display: flex;
    justify-content: space-between;
    margin-bottom: 20px;
  }
  .normal_content {
    width: 100%;
    height: calc(100vh - 230px);
    position: relative;
  }
  .full_content {
    width: 1920px;
    height: 100vh;
    position: fixed;
    left: 0;
    top: 0;
  }
  .content {
    background: url(~@/assets/page/model_bg.webp) no-repeat;
    background-size: 100% 100%;
    z-index: 999;
    #full-alarm::before,
    #full-alarm::after {
      content: "";
      position: absolute;
      width: 120px;
      height: 100%;
    }
    #full-alarm > div::before,
    #full-alarm > div::after {
      content: "";
      position: absolute;
      width: 100%;
      height: 120px;
    }
    #full-alarm::before {
      background: linear-gradient(to right, red, transparent);
      top: 0;
      left: 0;
      transform: rotate(0deg);
    }
    #full-alarm::after {
      background: linear-gradient(to left, red, transparent);
      top: 0%;
      left: 100%;
      transform: rotate(0deg) translate(calc(-1 * 120px), 0px);
    }
    #full-alarm > div::before {
      background: linear-gradient(to top, red, transparent);
      top: 0;
      left: 0;
      transform: rotate(180deg);
    }
    #full-alarm > div::after {
      background: linear-gradient(to top, red, transparent);
      top: 100%;
      left: 0;
      transform: rotate(0deg) translate(0px, calc(-1 * 120px));
    }
    #full-alarm {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      box-sizing: border-box;
      opacity: 1;
      transition: opacity 0.5s;
      pointer-events: none;
      z-index: 999;
      animation: blink 1s infinite;
    }
    .left_top {
      position: absolute;
      left: 20px;
      top: 10px;
      z-index: 999;
      .room_title {
        display: flex;
        align-items: center;
        // height: 40px;
        margin-top: -10px;
        .img1 {
          width: 50px;
          height: 60px;
          margin-top: 15px;
        }
        .img2 {
          width: 25px;
          height: 20px;
          margin: 0 5px;
        }
        div {
          // font-size: 22px !important;
          // padding-top: 10px;
          padding-right: 5px;
          font-family: PingFang SC;
          font-weight: bold;
          background: linear-gradient(0deg, #0279FA 25%, #E8EEFF 75%);
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
        }
      }
      .time {
        display: flex;
        margin-bottom: 20px;
        align-items: center;
        img {
          width: 15px;
          height: 15px;
          margin-right: 10px;
        }
        div {
          color: #91A0B4;
          font-size: 13px;
          font-weight: bold;
          display: flex;
        }
      }
      .info_box {
        width: 250px;
        background: url('../../../assets/page/kuang1.png') no-repeat;
        background-size: 100% 100%;
        padding: 20px 23px !important;
        .base_info {
          height: 300px !important;
          overflow: auto;
          .base_info_item {
            line-height: 27px;
            font-size: 13px;
            // font-weight: bold;
            // font-family: PingFang SC RE;
          }
        }
      }
    }
    .left_bottom {
      position: absolute;
      left: 20px;
      bottom: 30px;
      width: 220px;
      height: 65px;
      z-index: 999;
      background: url('../../../assets/page/kuang2.png') no-repeat;
      background-size: 100% 100%;
      display: flex;
      justify-content: space-between;
      font-size: 13px;
      .left_bottom_item {
        width: 25%;
        height: 90%;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        align-items: center;
        position: absolute;
        > :first-child {
          font-size: 16px;
        }
        div {
          // font-weight: bold;
        }
      }
      .low {
        top: -12px;
        left: 0;
        div:nth-child(1) {
          background: linear-gradient(0, #5BD943 0%, #FFFFFF 100%);
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
          font-weight: bold;
        }
      }
      .center {
        top: -12px;
        left: 40%;
        div:nth-child(1) {
          background: linear-gradient(0, #FAB237 0%, #FFFFFF 100%);
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
          font-weight: bold;
        }
      }
      .hign {
        top: -12px;
        right: 0;
        div:nth-child(1) {
          background: linear-gradient(0, #E53730 0%, #FFFFFF 100%);
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
          font-weight: bold;
        }
      }
    }
    .out_fullscreen {
      position: absolute;
      right: 50px;
      top: 20px;
      width: 50px;
      height: 50px;
      cursor: pointer;
      z-index: 999 !important;
    }
  }
  .dialog_footer {
    display: flex;
    justify-content: center;
    // margin-top: 20px;
  }
}
</style>
<style lang="scss">
.base_info {
  &::-webkit-scrollbar {
    width: 5px !important;
    cursor: pointer;
  }

  &::-webkit-scrollbar-track {
    height: 10px !important;
    width: 5px !important;
    background: rgba(81, 120, 192, 0.3);
    border-radius: 2px;
    cursor: pointer;
  }

  &::-webkit-scrollbar-thumb {
    height: 10px !important;
    width: 5px !important;
    background: rgba(30, 107, 248, 0.3);
    border-radius: 10px;
    cursor: pointer;
  }

  &::-webkit-scrollbar-thumb:hover {
    background: rgba(19, 77, 184, 0.3);
    cursor: pointer;
  }

  &::-webkit-scrollbar-corner {
    background: rgba(27, 95, 225, 0.3);
  }
}
.pagination {
  // width: 250px !important;
  margin-top: 20px !important;
  .el-pagination__total {
    min-width: 25px !important;
    height: 25px !important;
    line-height: 25px !important;
    color: rgb(196, 196, 196) !important;
    font-size: 12px !important;
  }
  button {
    background: transparent !important;
    color: rgb(196, 196, 196) !important;
    border: 1px solid #686a6b !important;
    min-width: 23px !important;
    height: 25px !important;
  }
  .number {
    background: transparent !important;
    color: rgb(196, 196, 196) !important;
    border: 1px solid #686a6b !important;
    min-width: 23px !important;
    height: 25px !important;
    line-height: 25px !important;
    margin: 0 3px !important;
    font-size: 12px !important;
  }
  .active {
    background: #006be4 !important;
    border: none !important;
    min-width: 23px !important;
    height: 25px !important;
    line-height: 25px !important;
  }
  .more {
    background: transparent !important;
    border: 1px solid #686a6b !important;
    min-width: 23px !important;
    height: 25px !important;
    line-height: 25px !important;
  }
}

.no_data {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: calc(100vh - 200px);
  img {
    width: 250px;
    height: 200px;
  }
  div {
    font-size: 20px;
    font-weight: bold;
    color: #7d7d7d;
  }
}
.list_container {
  height: calc(100% - 130px);
  overflow-y: auto;
  overflow-x: hidden;
  scrollbar-width: none;
}
</style>
