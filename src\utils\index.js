/* eslint-disable no-underscore-dangle */
/* eslint-disable radix */
/* eslint-disable no-bitwise */
/**
 * Created by PanJiaChen on 16/11/18.
 */

import dayjs from 'dayjs'

/**
 * Parse the time to string
 * @param {(Object|string|number)} time
 * @param {string} cFormat
 * @returns {string | null}
 */
export function parseTime(time, cFormat) {
  if (arguments.length === 0 || !time) {
    return null
  }
  const format = cFormat || '{y}-{m}-{d} {h}:{i}:{s}'
  let date
  if (typeof time === 'object') {
    date = time
  } else {
    if ((typeof time === 'string')) {
      if ((/^[0-9]+$/.test(time))) {
        // support "1548221490638"
        time = parseInt(time, 10)
      } else {
        // support safari
        // https://stackoverflow.com/questions/4310953/invalid-date-in-safari
        time = time.replace(new RegExp(/-/gm), '/')
      }
    }

    if ((typeof time === 'number') && (time.toString().length === 10)) {
      time *= 1000
    }
    date = new Date(time)
  }
  const formatObj = {
    y: date.getFullYear(),
    m: date.getMonth() + 1,
    d: date.getDate(),
    h: date.getHours(),
    i: date.getMinutes(),
    s: date.getSeconds(),
    a: date.getDay()
  }
  const time_str = format.replace(/{([ymdhisa])+}/g, (result, key) => {
    const value = formatObj[key]
    // Note: getDay() returns 0 on Sunday
    if (key === 'a') { return ['日', '一', '二', '三', '四', '五', '六'][value] }
    return value.toString().padStart(2, '0')
  })
  return time_str
}

/**
 * @param {number} time
 * @param {string} option
 * @returns {string}
 */
export function formatTime(time, option) {
  if ((`${time}`).length === 10) {
    time = parseInt(time, 10) * 1000
  } else {
    time = +time
  }
  const d = new Date(time)
  const now = Date.now()

  const diff = (now - d) / 1000

  if (diff < 30) {
    return '刚刚'
  } if (diff < 3600) {
    // less 1 hour
    return `${Math.ceil(diff / 60)}分钟前`
  } if (diff < 3600 * 24) {
    return `${Math.ceil(diff / 3600)}小时前`
  } if (diff < 3600 * 24 * 2) {
    return '1天前'
  }
  if (option) {
    return parseTime(time, option)
  }
  return (
    `${d.getMonth() +
      1
    }月${
      d.getDate()
    }日${
      d.getHours()
    }时${
      d.getMinutes()
    }分`
  )
}

/**
 * @param {string} url
 * @returns {Object}
 */
export function param2Obj(url) {
  const search = decodeURIComponent(url.split('?')[1]).replace(/\+/g, ' ')
  if (!search) {
    return {}
  }
  const obj = {}
  const searchArr = search.split('&')
  searchArr.forEach((v) => {
    const index = v.indexOf('=')
    if (index !== -1) {
      const name = v.substring(0, index)
      const val = v.substring(index + 1, v.length)
      obj[name] = val
    }
  })
  return obj
}
/**
 * @param {Function} func
 * @param {number} wait
 * @param {boolean} immediate
 * @return {*}
 */
// eslint-disable-next-line indent
 export function debounce(func, wait, immediate) {
  // eslint-disable-next-line one-var-declaration-per-line
  let timeout, args, context, timestamp, result

  // eslint-disable-next-line func-names
  const later = function() {
    // 据上一次触发时间间隔
    const last = +new Date() - timestamp

    // 上次被包装函数被调用时间间隔 last 小于设定时间间隔 wait
    if (last < wait && last > 0) {
      timeout = setTimeout(later, wait - last)
    } else {
      timeout = null
      // 如果设定为immediate===true，因为开始边界已经调用过了此处无需调用
      if (!immediate) {
        result = func.apply(context, args)
        // eslint-disable-next-line no-multi-assign
        if (!timeout) context = args = null
      }
    }
  }

  // eslint-disable-next-line no-shadow, func-names
  return function(...args) {
    context = this
    timestamp = +new Date()
    const callNow = immediate && !timeout
    // 如果延时不存在，重新设定延时
    if (!timeout) timeout = setTimeout(later, wait)
    if (callNow) {
      result = func.apply(context, args)
      // eslint-disable-next-line no-multi-assign
      context = args = null
    }

    return result
  }
}

/**
 * 图片压缩方法
 * @param {*} base64 图片base64
 * @param {*} w 压缩后图片宽度
 * @param {*} callback 回调，用于接收压缩结果
 */
export function compressImage(base64, w, callback) {
  const newImage = new Image()
  let quality = 0.9 // 压缩系数0-1之间
  newImage.src = base64
  newImage.setAttribute('crossOrigin', 'Anonymous')
  let imgWidth,
    imgHeight
  function onload() {
    imgWidth = this.width
    imgHeight = this.height
    const canvas = document.createElement('canvas')
    const ctx = canvas.getContext('2d')
    if (Math.max(imgWidth, imgHeight) > w) {
      if (imgWidth > imgHeight) {
        canvas.width = w
        canvas.height = (w * imgHeight) / imgWidth
      } else {
        canvas.height = w
        canvas.width = (w * imgWidth) / imgHeight
      }
    } else {
      canvas.width = imgWidth
      canvas.height = imgHeight
      quality = 0.9
    }
    ctx.clearRect(0, 0, canvas.width, canvas.height)
    ctx.drawImage(this, 0, 0, canvas.width, canvas.height)
    const result = canvas.toDataURL('image/jpeg', quality)
    // 如想确保图片压缩到自己想要的尺寸,如要求在50-150kb之间，请加以下语句，quality初始值根据情况自定
    // while (base64.length / 1024 > 150) {
    // quality -= 0.01;
    // base64 = canvas.toDataURL("image/jpeg", quality);
    // }
    // 防止最后一次压缩低于最低尺寸，只要quality递减合理，无需考虑
    // while (base64.length / 1024 < 50) {
    // quality += 0.001;
    // base64 = canvas.toDataURL("image/jpeg", quality);
    // }
    callback(result) // 必须通过回调函数返回，否则无法及时拿到该值
  }
  newImage.onload = onload
}
// 16进制转中文
export function utf8to16(str) {
  let out,
    i,
    len,
    c
  let char2,
    char3
  out = ''
  // eslint-disable-next-line prefer-const
  len = str.length
  i = 0
  while (i < len) {
    c = str.charCodeAt(i++)
    // eslint-disable-next-line default-case
    switch (c >> 4) {
      case 0:
      case 1:
      case 2:
      case 3:
      case 4:
      case 5:
      case 6:
      case 7:
        out += str.charAt(i - 1)
        break
      case 12:
      case 13:
        char2 = str.charCodeAt(i++)
        out += String.fromCharCode(((c & 0x1F) << 6) | (char2 & 0x3F))
        break
      case 14:
        char2 = str.charCodeAt(i++)
        char3 = str.charCodeAt(i++)
        out += String.fromCharCode(((c & 0x0F) << 12) |
          ((char2 & 0x3F) << 6) |
          // eslint-disable-next-line no-bitwise
          ((char3 & 0x3F) << 0))
        break
    }
  }

  return out
}

/**
 * This is just a simple version of deep copy
 * Has a lot of edge cases bug
 * If you want to use a perfect deep copy, use lodash's _.cloneDeep
 * @param {Object} source
 * @returns {Object}
 */
export function deepClone(source) {
  let target
  if (typeof source === 'object' && source) {
    target = Array.isArray(source) ? [] : {}
    for (const key in source) {
      // eslint-disable-next-line no-prototype-builtins
      if (source.hasOwnProperty(key) && source) {
        if (typeof source[key] !== 'object') {
          target[key] = source[key]
        } else {
          target[key] = deepClone(source[key])
        }
      } else {
        target = ''
      }
    }
  } else {
    target = source
  }
  return target
}

/**
 * 防抖
 * @param {Function} fn
 * @param {Number} delay
 * @returns {Function}
 */
export function simpleDebounce(fn, delay) {
  let timer = null
  // eslint-disable-next-line func-names
  return function() {
    if (timer) clearTimeout(timer)
    timer = setTimeout(() => {
      // eslint-disable-next-line prefer-rest-params
      fn.apply(this, arguments)
    }, delay)
  }
}

/**
 * @method formatDate
 * @description 格式化日期
 */
function padLeftZero(str) {
  return (`00${str}`).substr(str.length)
}
export function formatDate(date, fmt) {
  if (/(y+)/.test(fmt)) {
    fmt = fmt.replace(RegExp.$1, (`${date.getFullYear()}`).substr(4 - RegExp.$1.length))
  }
  const o = {
    'M+': date.getMonth() + 1,
    'd+': date.getDate(),
    'h+': date.getHours(),
    'm+': date.getMinutes(),
    's+': date.getSeconds()
  }
  for (const k in o) {
    if (new RegExp(`(${k})`).test(fmt)) {
      const str = `${o[k]}`
      fmt = fmt.replace(RegExp.$1, RegExp.$1.length === 1 ? str : padLeftZero(str))
    }
  }
  return fmt
}

export function timestampDifference(startTime, endTime) {
  // 这里取绝对值 保证结果为正
  const _timeInterval = Math.abs(endTime - startTime)

  let hours = Math.floor(parseInt(_timeInterval) / 1000 / 3600)
  let minutes = Math.floor(parseInt(_timeInterval) / 1000 / 60)
  let seconds = Math.floor(parseInt(_timeInterval) / 1000)
  // 取模处理 60进制
  minutes %= 60
  seconds %= 60
  // 判断是否为空
  let str = ''
  hours = hours ? `${hours}小时` : ''
  minutes = minutes ? `${minutes}分` : ''
  seconds = seconds ? `${seconds}秒` : ''
  str = hours + minutes + seconds
  return str
}

/**
 *防抖函数
 */
export function useDebounceFn(fn, ms = 100) {
  let timer = null
  return function(...args) {
    clearTimeout(timer)
    timer = setTimeout(() => {
      fn.apply(this, args)
    }, ms)
  }
}

/**
 * 根据日期类型和时间返回时间范围
 * @param {String} date 日期
 * @param {Number} type 0: 日 1: 周 2: 月 3: 年 时间类型
 * */
export function getDateRange(date, type, format = 'YYYY-MM-DD HH:mm:ss') {
  let start = ''
  let end = ''
  switch (type) {
    case 0:
      start = dayjs(date).startOf('day').format(format)
      end = dayjs(date).endOf('day').format(format)
      break
    case 1:
      start = dayjs(date).startOf('week').format(format)
      end = dayjs(date).endOf('week').format(format)
      break
    case 2:
      start = dayjs(date).startOf('month').format(format)
      end = dayjs(date).endOf('month').format(format)
      break
    case 3:
      start = dayjs(date).startOf('year').format(format)
      end = dayjs(date).endOf('year').format(format)
      break
    default:
      break
  }
  return { start, end }
}

/**
 * 判断是否为空
 */
export function validatenull(val) {
  if (typeof val === 'boolean') {
    return false
  }
  if (typeof val === 'number') {
    return false
  }
  if (val instanceof Array) {
    if (val.length === 0) return true
  } else if (val instanceof Object) {
    if (JSON.stringify(val) === '{}') return true
  } else {
    if (val === 'null' || val === null || val === 'undefined' || val === undefined || val === '') return true
    return false
  }
  return false
}

/**
 * 计算两个点之间的距离
 * */
export function calculateDistance(point1, point2) {
  const dx = point2.x - point1.x
  const dy = point2.y - point1.y
  return Math.sqrt(dx * dx + dy * dy)
}

export function changeFavicon(link) {
  let $favicon = document.querySelector('link[rel="icon"]')
  // If a <link rel="icon"> element already exists,
  // change its href to the given link.
  if ($favicon !== null) {
    $favicon.href = link
    // Otherwise, create a new element and append it to <head>.
  } else {
    $favicon = document.createElement('link')
    $favicon.rel = 'icon'
    $favicon.href = link
    document.head.appendChild($favicon)
  }
}

/**
 * 树级数据扁平化
 * */
export function flatData(data) {
  const arr = []
  data.forEach((item) => {
    arr.push(item)
    if (item.childList && item.childList.length) {
      arr.push(...flatData(item.childList))
    }
  })
  return arr
}
