<template>
  <div
    :id="id"
    :style="{ height: height, width: width }"
  />
</template>

<script>
import chartsMixIn from './mixins'

export default {
  name: 'EconomicChange',
  mixins: [chartsMixIn],
  props: {
    id: {
      require: true,
      type: String,
      default: 'charts'
    },
    width: {
      require: false,
      type: String,
      default: '100%'
    },
    height: {
      require: false,
      type: String,
      default: '100%'
    },
    propData: {
      require: false,
      type: Object,
      default: () => {}
    }
  },
  mounted() {
    this.$nextTick(() => {
      this.initChart()
    })

    console.log(this.propData, 888888888)
  },
  methods: {
    initChart() {
      this.chart = this.$echarts.init(document.getElementById(this.id))
      // this.showLoading(this.chart)
      // 模拟接口获取propData过程显示、隐藏加载动画，实际开发需去掉该定时器在watch中调用hideLoading方法
      // setTimeout(() => {
      //   // 隐藏加载动画
      //   this.hideLoading(this.chart)
      //   // 调用动画
      //   this.autoPlayTool(7)
      // }, 3000)
      this.option = {
        // 提示信息
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'cross',
            crossStyle: {
              color: '#999'
            }
          }
        },
        toolbox: {
          // feature: {
          //   dataView: { show: true, readOnly: false },
          //   magicType: { show: true, type: ['line', 'bar'] },
          //   restore: { show: true },
          //   saveAsImage: { show: true }
          // }
        },
        legend: {
          data: []
        },
        xAxis: [
          {
            type: 'category',
            data: this.propData.data,
            axisPointer: {
              type: 'shadow'
            }
          }
        ],
        yAxis: [
          {
            type: 'value',
            name: '（万元）',
            min: 0,
            // max: 250,
            // interval: 50,
            axisLabel: {
              formatter: '{value}'
            }
          },
          {
            type: 'value',
            name: '%',
            min: 0,
            max: 100,
            // interval: 5,
            axisLabel: {
              formatter: '{value}'
            }
          }
        ],
        series: this.propData.series
      }

      this.chart.setOption(this.option)
      // this.hideLoading(this.chart)
    }
  }
}
</script>
