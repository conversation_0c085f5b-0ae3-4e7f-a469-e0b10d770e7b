<template>
  <div style="position:relative;">
    <div
      id="lineCharts"
      style="height: calc(100vh - 9.5rem); width: 100%;"
    />
    <div
      class="remark"
    >
      <div class="remark_item">
        <div class="dot dot2" />
        <div class="text">超过阈值</div>
      </div>
    </div>
    <!-- 历史数据图表数据 -->
    <el-dialog
      v-if="dialogVisible"
      title="历史数据"
      :visible.sync="dialogVisible"
      width="fit-content"
      top="90px"
      append-to-body
      destroy-on-close
    >
      <PointDetailChart
        ref="chartRef"
        :data="formData"
      />
    </el-dialog>

  </div>
</template>
<script>
import * as echarts from 'echarts'
import { deepClone } from '@/utils'
import PointDetailChart from '@/views/aobo/fieldView/components/PointDetailChart'

export default {
  name: 'SensorChart',
  components: { PointDetailChart },
  props: {
    zoomStart: {
      require: true,
      type: Number,
      default: 0
    },
    zoomEnd: {
      require: true,
      type: Number,
      default: 100
    },
    threshold: {
      require: true,
      type: Number,
      default: 0
    },
    sensor: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      zoomStartChild: 0,
      zoomEndChild: 100,
      dialogVisible: false,
      // 详情数据
      formData: null,

    }
  },
  created() {
    window.handle = this.handle
  },
  mounted() {
    window.addEventListener('resize', this.onResize)
    this.zoomStartChild = this.zoomStart
    this.zoomEndChild = this.zoomEnd
    this.$nextTick(() => {
      this.initcharts()
    })
  },
  destroyed() {
    window.removeEventListener('resize', this.onResize)
  },

  methods: {
    // 点击跳转
    handle(row) {
      this.formData = { sensorCode: this.sensor.code, pointSort: row.sort }
      this.dialogVisible = true
      console.log(row)
    },
    initcharts() {
      this.chart = echarts.init(document.getElementById('lineCharts'))
      this.chart.on('dataZoom', (e) => {
        console.log('data zoom')
        this.zoomStartChild = e.batch[0].start
        this.zoomEndChild = e.batch[0].end
        this.$emit('zoomChange', {
          zoomStart: e.batch[0].start,
          zoomEnd: e.batch[0].end
        })
      })
      this.chart.on('legendselectchanged', (params) => {
        console.log(params, 'legendselectchanged')
        this.$emit('legendselectchanged', {
          chartsSelected: params.selected
        })
      })
    },
    updateOption(list) {
      const colorList = [
        '#5470c6',
        '#91cc75',
        '#fac858',
        '#ee6666',
        '#73c0de',
        '#3ba272',
        '#fc8452',
        '#9a60b4',
        '#ea7ccc'
      ]
      const that = this
      const option = {
        legend: {
          type: 'plain',
          top: 0,
          right: 200,
          itemGap: 50,
          itemWidth: 20,
          itemHeight: 7,
          icon: 'roundRect',
        },
        grid: {
          left: 80,
          top: 40,
          bottom: 10,
          right: 80,
          containLabel: true
        },
        dataZoom: [{
          // filterMode: 'none',
          type: 'inside',
          start: this.zoomStartChild,
          end: this.zoomEndChild
          // realtime: false
        }],
        xAxis: {
          name: '点位',
          type: 'category',
          nameTextStyle: {
            color: '#9FA3AB',
            fontSize: 14
          },
          axisLine: {
            lineStyle: {
              color: '#8F98A0'
            }
          },
          axisTick: { length: 0 },
          axisLabel: {
            textStyle: {
              color: '#909090'
            },
            fontSize: 14,
          },
          splitLine: {
            show: false,
            lineStyle: {
              type: 'dashed'
            }
          },
          data: list.map((item) => item.name),
          animationDuration: 300,
          animationDurationUpdate: 300,

        },
        yAxis: {
          name: '单位：mm',
          type: 'value',
          axisLine: { show: false },
          axisLabel: {
            color: '#9FA3AB'
            // formatter: (value) => `${(value * 100).toFixed(1)}%`
          },
          nameTextStyle: {
            color: '#9FA3AB',
            fontSize: 14,
            align: 'center',
            padding: [0, 0, 5, 0]
          },
          // max(value) {
          //   return value.max + 2
          // },
          splitLine: {
            show: true,
            lineStyle: {
              type: 'dashed'
            }
          },
          splitNumber: 10,
        },
        tooltip: {
          trigger: 'axis',
          triggerOn: 'click',
          enterable: true,
          padding: [12, 15, 20, 20],
          textStyle: { color: '#424242' },
          renderMode: 'html',
          className: 'tooltip',
          formatter(params) {
            let html = ``
            params.forEach((v) => {
              const { data } = v
              html += `<div style="margin-bottom:10px;">
                  <div style="display:flex;align-items:center">
                    <div
                      style="
                        margin-right:10px;
                        border-radius:6px;
                        width:23px;height:7px;
                        background-color:${colorList[v.componentIndex]};
                      "
                    ></div>
                    <div style="margin-right:20px;">
                    <span>${data.name}</span>
                    <span style="color:#1768EB;cursor:pointer;" onclick='handle(${JSON.stringify(data)})' >历史数据</span>
                    </div>
                  </div>
                  <div style="display:flex;align-items:center">
                    <div>
                      ${data.value}mm
                    </div>

                  </div>
                </div>`
            })

            return html
          },

        },
        series: {
          name: '位移',
          type: 'line',
          data: list,
          smooth: true, // 平滑曲线
          sampling: 'average',
          large: true,
          barMaxWidth: 30,
          symbolSize(params) {
            return params[1] > that.threshold / 100 ? 8 : 0
          },
          itemStyle: {
            normal: {
              color(params) {
                const thresholdValue = that.threshold / 100
                // params 是当前数据点的信息
                return params.value[1] > thresholdValue ? 'red' : '#5470c6'
              }
            }
          },
          lineStyle: { width: 2.5, color: '#5470c6' },
          areaStyle: {
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              { offset: 0, color: 'rgba(84, 112, 198, 0)' },
              { offset: 0.9, color: '#fff' }
            ])
          },
          label: {
            show: false,
            position: 'top',
            formatter: (val) => `${(val.value / 100).toFixed(2)}℃`
          },
          // animationDuration: 0,
          animationDurationUpdate: 1000,
          animationEasing: 'linear',
          animationEasingUpdate: 'linear'
        }
      }
      this.chart.setOption(option)
    },
    onResize() {
      this.chart.resize()
    }

  }
}
</script>
<style lang="scss" scoped>
.remark {
  position: absolute;
  top: 0;
  right: 20px;
  display: flex;
  justify-content: space-between;
  width: 130px;
  height: 20px;
  // line-height: 50px;
  .remark_item {
    display: flex;
    align-items: center;
    .dot {
      // width: 21px;
      // height: 6px;
      // margin-right: 5px;
      // border-radius: 2px;
      width: 10px;
      height: 10px;
      margin-right: 5px;
      border-radius: 10px;
    }
    .dot1 {
      background-color: #00E667;
    }
    .dot2 {
      background-color: red;
    }
    .text {
      color: black;
      font-family: PingFang SC RE;
      // font-weight: bold;
      font-size: 14px;
    }
  }
}
</style>
