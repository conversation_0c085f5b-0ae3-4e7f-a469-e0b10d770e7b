<template>
  <div
    v-loading="loading"
    style="position:relative;"
  >
    <el-date-picker
      v-model="date"
      type="date"
      placeholder="选择日期"
      :clearable="false"
      value-format="yyyy-MM-dd"
      format="yyyy-MM-dd"
      append-to-body
      :picker-options="{disabledDate}"
      @change="handleQuery"
    />
    <div
      id="detailChart"
      style="height: 500px; width: 58vw;"
    />
  </div>
</template>
<script>
import * as echarts from 'echarts'
import dayjs from 'dayjs'
import { pointHistory } from '@/api/lineCharts'

const alarmColorMap = {
  0: '#0000FF',
  1: '#FFA500',
  2: '#e4e46f',
  3: '#FF0000',
}

export default {
  name: 'PointDetailChart',
  props: {
    data: {
      require: true,
      type: Object,
      default: () => {}
    },
    alarmLevel: {
      type: Array,
      default: () => []
    }

  },
  data() {
    return {
      zoomStartChild: 0,
      zoomEndChild: 100,
      // 温度数据
      dataList: [],
      loading: false,
      retentionDay: null,
      date: dayjs().format('YYYY-MM-DD'),
    }
  },
  created() {
    this.handleQuery()
  },
  mounted() {
    console.log(this.data)
    // this.$nextTick(() => {
    //   this.initcharts()
    // })
  },
  methods: {
    handleQuery() {
      this.loading = true
      pointHistory({ sensorCode: this.data.sensorCode, pointSort: this.data.pointSort, date: this.date })
        .then((res) => {
          this.retentionDay = res.data.retentionDay
          this.dataList = res.data.histories
          this.updateOption()
        }).finally(() => {
          this.loading = false
        })
    },
    updateOption() {
      this.chart = echarts.init(document.getElementById('detailChart'))
      const xData = []
      const yData = []
      for (const item of this.dataList) {
        xData.push(item.time)
        yData.push(item.value)
      }
      const option = {
        legend: {
          type: 'plain',
          top: 0,
          right: 200,
          itemGap: 50,
          itemWidth: 20,
          itemHeight: 7,
          icon: 'roundRect'
        },
        grid: {
          left: 80,
          top: 40,
          bottom: 10,
          right: 110,
          containLabel: true
        },
        dataZoom: [{
          // filterMode: 'none',
          type: 'inside',
          start: 0,
          end: 10
          // realtime: false
        }],
        xAxis: {
          name: '时间',
          type: 'category',
          data: xData,
          nameTextStyle: {
            color: '#9FA3AB',
            fontSize: 14
          },
          axisLine: {
            lineStyle: {
              color: '#8F98A0'
            }
          },
          axisTick: { length: 0 },
          axisLabel: {
            textStyle: {
              color: '#909090'
            },
            fontSize: 14
            // formatter: (value) => `${(value / 100).toFixed(2)}m`
          },
          splitLine: {
            show: false,
            lineStyle: {
              type: 'dashed'
            }
          }
        },
        yAxis: {
          name: '单位：mm',
          type: 'value',
          axisLine: { show: false },
          axisLabel: {
            color: '#9FA3AB'
            // formatter: (value) => `${(value * 100).toFixed(1)}%`
          },
          nameTextStyle: {
            color: '#9FA3AB',
            fontSize: 14,
            align: 'center',
            padding: [0, 0, 5, 0]
          },
          // max(value) {
          //   return value.max + 2
          // },
          splitLine: {
            show: true,
            lineStyle: {
              type: 'dashed'
            }
          },
          splitNumber: 10
        },
        tooltip: {
          trigger: 'axis',
          triggerOn: 'click',
          enterable: true,
          padding: [12, 15, 20, 20],
          textStyle: { color: '#424242' },
          formatter: '{b}<br /> {c}mm'
        },
        series: [{
          // name: el.name,
          type: 'line',
          data: yData,
          smooth: true, // 平滑曲线
          symbolSize: 8,
          showAllSymbol: false,
          sampling: 'average',
          // large: true,
          symbol: 'none',
          // itemStyle: { color: '#1768EB' },
          // lineStyle: { width: 2.5, color: '#1768EB' },
          lineStyle: { width: 2.5 },
          // areaStyle: {
          //   color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
          //     { offset: 0, color: 'rgba(48, 149, 251, 0.4)' },
          //     { offset: 0.9, color: '#fff' }
          //   ])
          // },
          label: {
            show: false,
            position: 'top',
            formatter: (val) => `${val.value}mm`
          }
        }]
      }
      // 添加阈值线，正负两条
      const alarmLevel = this.alarmLevel.map((item) => [item, { ...item, value: -item.value }]).flat()
      for (const item of alarmLevel) {
        option.series.push({
          name: '',
          type: 'line',
          data: [item.value],
          symbol: 'none',
          label: {
            show: false
          },
          markLine: {
            silent: true,
            data: [{
              name: `报警线：${item.value}mm`,
              yAxis: item.value,
              label: {
                formatter: '{b}',
                position: 'end',
                color: alarmColorMap[item.level],
              }
            }],
            lineStyle: {
              color: alarmColorMap[item.level],
              width: 2
            },
            symbol: 'none',
            label: {
              distance: [0, 8]
            }
          }
        })
      }

      this.chart.setOption(option)
    },
    disabledDate(time) {
      return time.getTime() > new Date().getTime() || time.getTime() < new Date().getTime() - this.retentionDay * 24 * 60 * 60 * 1000
    },

  }
}
</script>
<style lang="scss" scoped></style>
