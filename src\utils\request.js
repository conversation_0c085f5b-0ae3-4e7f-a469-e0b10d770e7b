import axios from 'axios'
import { MessageBox, Message } from 'element-ui'
// eslint-disable-next-line import/no-cycle
import store from '@/store'
import { getToken } from '@/utils/auth'
import router, { resetRouter } from '@/router/index'
import Cookies from 'js-cookie'

// 转换错误信息
const translate = (errMessage) => {
  if (errMessage.includes('Request failed with status code')) {
    return errMessage.replace('Request failed with status code', '服务请求失败 状态码为:')
  }

  if (errMessage.includes('timeout of')) {
    return '服务请求超时 20000ms'
  }

  return errMessage
}

// create an axios instance
const { CancelToken } = axios
const source = CancelToken.source()
const service = axios.create({
  // baseURL: process.env.VUE_APP_BASE_API, // url = base url + request url
  withCredentials: true, // send cookies when cross-domain requests
  timeout: 25000 // request timeout
})
let refuseToAccept = false

// request interceptor
service.interceptors.request.use(
  (config) => {
    const token = getToken()
    // if (token) {
    //   config.headers.Authorization = getToken()
    //   config.cancelToken = source.token
    // }
    if (token) {
      // let each request carry token
      // ['X-Token'] is a custom headers key
      // please modify it according to the actual situation
      config.headers.Authorization = getToken() || ''
      config.headers['auth-token'] = getToken() || ''
      config.cancelToken = source.token
    }
    return config
  },
  (error) => {
    // do something with request error
    console.log(error) // for debug
    return Promise.reject(error)
  }
)

// response interceptor
service.interceptors.response.use(
  /**
   * If you want to get http information such as headers or status
   * Please return  response => response
  */

  /**
   * Determine the request status by custom code
   * Here is just an example
   * You can also judge the status by HTTP Status Code
   */
  (response) => {
    if (!Cookies.get('governance_token') || Cookies.get('governance_token') === 'undefined') {
      Cookies.set('governance_token', response.headers['auth-token'])
    }
    const res = response.data
    // 正常成功
    if (res.code === 200) {
      refuseToAccept = false
      return res
    }
    // 文件流之际返回
    if (res instanceof Blob) {
      if (res.type !== 'application/json') {
        refuseToAccept = false
        return response
      }
      const reader = new FileReader()
      reader.onload = (e) => {
        if (e.target.readyState === 2) {
          let res = {}
          res = JSON.parse(e.target.result)
          Message({
            message: res.msg || 'Error',
            type: 'error',
            duration: 5 * 1000
          })
        }
      }
      reader.readAsText(res)
      return false
    }
    // 鉴权出错后续请求不再弹出登录提示弹框
    if (refuseToAccept) {
      return false
    }
    if (res.code === 21001) {
      refuseToAccept = true
      MessageBox.confirm(res.msg, '提示', {
        confirmButtonText: '确定',
        showCancelButton: false,
        type: 'warning'
      }).then(() => {
        store.dispatch('user/resetToken').then(() => {
          store.commit('permission/HAS_FILTER_ROUTES', false)
          router.push(`/login`)
        })
      })
      return false
    }
    // token过期
    if (res.code === 403 || res.code === 405) {
      refuseToAccept = true
      Message({
        message: res.msg || 'Error',
        type: 'error',
        duration: 5 * 1000
      })
      // MessageBox.confirm(res.msg, '提示', {
      //   confirmButtonText: '确定',
      //   showCancelButton: false,
      //   type: 'warning'
      // }).then(() => {
      //   store.dispatch('user/resetToken').then(() => {
      //     store.commit('permission/HAS_FILTER_ROUTES', false)
      //     resetRouter()
      //     router.push(`/login`)
      //   })
      // })
      store.dispatch('user/resetToken').then(() => {
        store.commit('permission/HAS_FILTER_ROUTES', false)
        resetRouter()
        router.push(`/login`)
      })
      return false
    }
    // 登录验证码
    if (res.code === 422 || res.code === 423 || res.code === 3001) {
      Message({
        message: res.msg || 'Error',
        type: 'error',
        duration: 5 * 1000
      })
      return res
    }
    // 其他的错误情况
    Message({
      message: res.msg || 'Error',
      type: 'error',
      duration: 5 * 1000
    })
    return Promise.reject(new Error(res.msg || 'Error'))
  },
  (error) => {
    if (axios.isCancel(error)) {
      console.log('Request canceled', error.message)
    } else {
      Message({
        message: translate(error),
        type: 'error',
        duration: 5 * 1000
      })
    }
    return Promise.reject(error)
  }
)

export default service
