<template>
  <div class="main">
    <!-- <pdf
      v-for="i in numPages"
      :key="i"
      :page="i"
      :src="pdfUrl"
      style="width: 100%; height: auto;"
      @num-pages="pageCount=$event"
    />
    <i
      class="el-icon-circle-close"
      @click="close"
    /> -->
  </div>
</template>

<script>
// import pdf from 'vue-pdf'

export default {
  // components: {
  //   pdf
  // },
  // props: {
  //   src: {
  //     type: String,
  //     default: ''
  //   }
  // },
  // data() {
  //   return {
  //     pageCount: 0,
  //     pdfUrl: '',
  //     numPages: 0
  //   }
  // },
  // mounted() {
  //   this.loadPdfHandler()
  // },

  // methods: {
  //   loadPdfHandler() {
  //     this.pdfUrl = pdf.createLoadingTask(this.src)
  //     this.pdfUrl.promise.then((e) => {
  //       this.numPages = e.numPages
  //     })
  //   },
  //   close() {
  //     this.$emit('close')
  //   }
  // }
}
</script>

<style lang="scss" scoped>
.main{
  z-index: 99999;
  position: absolute;
  top: 0;
  width: 100%;
  height: 100%;
  // overflow-y: scroll;
}
.el-icon-circle-close{
  font-size: 40px;
  position: fixed;
  top: 100px;
  right: 50px;
  cursor: pointer;
}
</style>
