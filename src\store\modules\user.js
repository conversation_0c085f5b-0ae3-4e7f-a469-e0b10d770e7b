// eslint-disable-next-line import/no-cycle
import { login, logout } from '@/api/user'
import {
  getToken, setUserInfo, removeToken
} from '@/utils/auth'
import { resetRouter } from '@/router'

const state = {
  token: getToken(),
  name: '',
  avatar: '',
  btnAuthority: window.localStorage.getItem('btnAuthority')
    ? JSON.parse(window.localStorage.getItem('btnAuthority')) : [],
  userInfo: window.localStorage.getItem('userInfo') ? JSON.parse(window.localStorage.getItem('userInfo')) : {}
}

const mutations = {
  SET_TOKEN: (state, token) => {
    state.token = token
  },
  SET_NAME: (state, name) => {
    window.sessionStorage.setItem('name', JSON.stringify(name))
    state.name = name
  },
  SET_AVATAR: (state, avatar) => {
    state.avatar = avatar
  },
  SET_USERINFO: (state, userInfo) => {
    state.userInfo = userInfo
  },
  SET_BTNAUTHORITY: (state, btnAuthority) => {
    state.btnAuthority = btnAuthority
  }
}

const actions = {
  // user login
  login({ commit }, userInfo) {
    const { username, password } = userInfo
    return new Promise((resolve, reject) => {
      // login({ principal: username.trim(), password, imageCode: code }).then((response) => {
      //   const { data } = response
      //   if (data) {
      //     commit('SET_TOKEN', data.token)
      //     commit('SET_NAME', data.principal.nickName || '管理员')
      //     setToken(data.token)
      //     setUserInfo(data)
      //     localStorage.setItem('menuList', JSON.stringify(data.userDetail.menuList))
      //     localStorage.setItem('userInfo', JSON.stringify(data.principal))
      //   }
      //   resolve(response)
      // }).catch((error) => {
      //   reject(error)
      // })
      const formData = new FormData()
      formData.append('username', username.trim())
      formData.append('password', password)
      // formData.append('type', 'manager')
      formData.append('type', 'account')
      login(formData).then((response) => {
        const { data } = response
        if (data) {
          // commit('SET_TOKEN', data.token)
          commit('SET_NAME', data.actualName || '管理员')
          data.detail.nickname = data.detail.nickname || '管理员'
          commit('SET_USERINFO', data.detail)
          setUserInfo(data)
          // eslint-disable-next-line no-use-before-define
          console.log(findItem(data.detail.menuAuthority), findItemCode(data.detail.menuAuthority))
          // eslint-disable-next-line no-use-before-define
          const webCode = findItem(data.detail.menuAuthority)
          // eslint-disable-next-line no-use-before-define
          const btnAuthority = findItemCode(data.detail.menuAuthority)
          localStorage.setItem('menuList', JSON.stringify(webCode)) // 菜单权限
          localStorage.setItem('userInfo', JSON.stringify(data.detail))
          localStorage.setItem('actualName', JSON.stringify(data.actualName))
          localStorage.setItem('btnAuthority', JSON.stringify(btnAuthority)) // code与按钮权限
          commit('SET_BTNAUTHORITY', btnAuthority)
        }
        resolve(response)
      }).catch((error) => {
        reject(error)
      })
    })
  },

  logout({ commit, state }) {
    return new Promise((resolve, reject) => {
      logout(state.token).then(() => {
        commit('SET_TOKEN', '')
        localStorage.clear()
        sessionStorage.clear()
        removeToken()
        resetRouter()
        commit('permission/HAS_FILTER_ROUTES', false, { root: true })
        resolve()
      }).catch((error) => {
        reject(error)
      })
    })
  },
  resetToken({ commit }) {
    return new Promise((resolve) => {
      commit('SET_TOKEN', '')
      localStorage.removeItem('permissionList')
      removeToken()
      resetRouter()
      resolve()
    })
  },
  // 设置用户信息
  setUserInfo({ commit }, userInfo) {
    commit('SET_USERINFO', userInfo)
  }
}

// 递归获取code值
function findItem(arr) {
  return arr.reduce((a, b) => {
    let res = [...a, b.code]
    if (b.childList) res = [...res, ...findItem(b.childList)]
    return res
  }, [])
}
// 递归获取code值与按钮权限数组
function findItemCode(arr) {
  return arr.reduce((a, b) => {
    let res = [...a, { code: b.code, functionPermissionCode: b.functionPermissionCode }]
    if (b.childList) res = [...res, ...findItemCode(b.childList)]
    return res
  }, [])
}
export default {
  namespaced: true,
  state,
  mutations,
  actions
}

