const getters = {
  sidebar: (state) => state.app.sidebar,
  device: (state) => state.app.device,
  token: (state) => state.user.token,
  avatar: (state) => state.user.avatar,
  userInfo: (state) => state.user.userInfo,
  btnAuthority: (state) => state.user.btnAuthority,
  name: (state) => state.user.name,
  tableHeaderStyle: (state) => state.style.tableHeaderStyle,
  showDetail: (state) => state.investmentStatistics.showDetail,
  showRecords: (state) => state.investmentStatistics.showRecords,
  isLookOver: (state) => state.investmentStatistics.isLookOver,
  enterpriseDetails: (state) => state.investmentStatistics.enterpriseDetails,
  hasFilterAsyncRoutes: (state) => state.permission.hasFilterAsyncRoutes,
  addRoutes: (state) => state.permission.addRoutes,
  threeJsLoaded: (state) => state.threeJs.loaded,
  threeJsProgress: (state) => state.threeJs.progress,
  routes: (state) => state.permission.routes
}
export default getters
