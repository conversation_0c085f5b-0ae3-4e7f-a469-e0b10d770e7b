<template>
  <div>
    <el-button
      v-if="tagName !== 'IMPORT'"
      :disabled="disabled"
      :type="typeName"
      size="mini"
      @click="btnClick(tagName)"
    >{{ text }}</el-button>
    <el-upload
      v-else-if="tagName === 'IMPORT'"
      action="#"
      :show-file-list="false"
      :auto-upload="false"
      :on-change="handleImport"
      :disabled="disabled"
    >
      <el-button
        :disabled="disabled"
        :type="typeName"
        plain
        size="mini"
      >{{ text }}</el-button>
    </el-upload>
  </div>
</template>

<script>

export default {
  name: 'TopButton',
  components: {},
  props: {
    text: {
      type: String,
      default: ''
    },
    tagName: {
      type: String,
      default: ''
    },
    typeName: {
      type: String,
      default: 'primary'
    },
    disabled: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {}
  },
  created() {},
  methods: {
    btnClick(tagName, filr) {
      this.$emit('click', tagName, filr)
    },
    handleImport(file) {
      if (this.disabled) {
        return
      }
      this.btnClick(this.tagName, file)
    }
  }
}
</script>

<style lang="scss" scoped>
.custom-button {
  padding: 7px 13px;
  border-color: #fff;
  border-radius: 6px;
  height: 32px;
  font-size: 16px;
  font-family: Source Han Sans CN;
  font-weight: 500;
}
.roleDisableDiv {
  cursor: not-allowed !important;
  .roleDisable {
    pointer-events: none;
  }
}
</style>
