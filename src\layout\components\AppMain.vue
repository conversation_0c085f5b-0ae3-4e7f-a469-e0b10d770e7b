<template>
  <section class="app-main">
    <transition
      name="fade-transform"
      mode="out-in"
    >
      <router-view :key="key" />
    </transition>
  </section>
</template>

<script>
export default {
  name: 'AppMain',
  computed: {
    key() {
      return this.$route.path
    }
  }
}
</script>

<style scoped>
.app-main {
  /*50 = navbar  */
  height: calc(100vh - 110px);
  width: 1730px;
  overflow-y: auto;
  border-radius: 15px;
  padding: 0 30px 0;
  box-sizing: border-box;
  position: relative;
  color: #595959;
  background: #fff;
  font-size: 15px;
  margin-left: 20px;
}
  *::-webkit-scrollbar {
    display: none;
  }
.fixed-header+.app-main {
  padding-top: 50px;
}
</style>

<style lang="scss">
// fix css style bug in open el-dialog
.el-popup-parent--hidden {
  .fixed-header {
    padding-right: 15px;
  }
}
</style>
