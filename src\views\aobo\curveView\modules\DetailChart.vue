<template>
  <div
    v-loading="loading"
    style="position:relative;"
  >
    <el-date-picker
      v-model="date"
      type="date"
      placeholder="选择日期"
      :clearable="false"
      value-format="yyyy-MM-dd"
      format="yyyy-MM-dd"
      append-to-body
      :picker-options="{disabledDate}"
      @change="handleQuery"
    />

    <div v-if="dataList.length">
      <div
        id="detailChart"
        style="height: 500px; width: 58vw;"
      />

    </div>
    <div v-else style="height: 500px; width: 58vw;display: grid;place-items: center;user-select: none">暂无数据</div>

  </div>
</template>
<script>
import * as echarts from 'echarts'
import { temperatureHistory } from '@/api/deviceManage'
import dayjs from 'dayjs'

export default {
  name: 'DetailChart',
  props: {
    data: {
      require: true,
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      zoomStartChild: 0,
      zoomEndChild: 100,
      // 温度数据
      dataList: [],
      loading: false,
      date: dayjs().format('YYYY-MM-DD'),
      retentionDay: null,
    }
  },
  created() {
    this.handleQuery()
  },
  mounted() {
    console.log(this.data)
    // this.$nextTick(() => {
    //   this.initcharts()
    // })
  },
  methods: {
    handleQuery() {
      this.loading = true
      temperatureHistory({ position: this.data.position, cableId: this.data.cableId, date: this.date })
        .then((res) => {
          this.dataList = res.data.histories
          this.retentionDay = res.data.retentionDay
          if (this.dataList.length) {
            this.$nextTick(() => {
              this.updateOption()
            })
          } else {
            this.chart?.clear()
            this.$message({
              message: '暂无数据',
              type: 'warning'
            })
          }
        }).finally(() => {
          this.loading = false
        })
    },
    updateOption() {
      this.chart = echarts.init(document.getElementById('detailChart'))
      const colorList = [
        '#5470c6',
        '#91cc75',
        '#fac858',
        '#ee6666',
        '#73c0de',
        '#3ba272',
        '#fc8452',
        '#9a60b4',
        '#ea7ccc'

      ]
      const xData = []
      const yData = []
      for (const item of this.dataList) {
        xData.push(item.time)
        yData.push((item.temperature / 100).toFixed(2))
      }
      const option = {
        legend: {
          type: 'plain',
          top: 0,
          right: 200,
          itemGap: 50,
          itemWidth: 20,
          itemHeight: 7,
          icon: 'roundRect'
        },
        grid: {
          left: 80,
          top: 40,
          bottom: 10,
          right: 80,
          containLabel: true
        },
        dataZoom: [{
          // filterMode: 'none',
          type: 'inside',
          start: 0,
          end: 10
          // realtime: false
        }],
        xAxis: {
          name: '时间',
          type: 'category',
          data: xData,
          nameTextStyle: {
            color: '#9FA3AB',
            fontSize: 14
          },
          axisLine: {
            lineStyle: {
              color: '#8F98A0'
            }
          },
          axisTick: { length: 0 },
          axisLabel: {
            textStyle: {
              color: '#909090'
            },
            fontSize: 14
            // formatter: (value) => `${(value / 100).toFixed(2)}m`
          },
          splitLine: {
            show: false,
            lineStyle: {
              type: 'dashed'
            }
          }
        },
        yAxis: {
          name: '温度：℃',
          type: 'value',
          axisLine: { show: false },
          axisLabel: {
            color: '#9FA3AB'
            // formatter: (value) => `${(value * 100).toFixed(1)}%`
          },
          nameTextStyle: {
            color: '#9FA3AB',
            fontSize: 14,
            align: 'center',
            padding: [0, 0, 5, 0]
          },
          // max(value) {
          //   return value.max + 2
          // },
          splitLine: {
            show: true,
            lineStyle: {
              type: 'dashed'
            }
          },
          splitNumber: 10
        },
        tooltip: {
          trigger: 'axis',
          triggerOn: 'click',
          enterable: true,
          padding: [12, 15, 20, 20],
          textStyle: { color: '#424242' },
          formatter: '{b}<br /> {c}℃'
        },
        series: {
          // name: el.name,
          type: 'line',
          data: yData,
          smooth: true, // 平滑曲线
          symbolSize: 8,
          showAllSymbol: true,
          // showSymbol: true,
          symbol: 'circle',
          // itemStyle: { color: '#1768EB' },
          // lineStyle: { width: 2.5, color: '#1768EB' },
          lineStyle: { width: 2.5 },
          areaStyle: {
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              { offset: 0, color: 'rgba(48, 149, 251, 0.4)' },
              { offset: 0.9, color: '#fff' }
            ])
          },
          label: {
            show: false,
            position: 'top',
            formatter: (val) => `${(val.value / 100).toFixed(2)}℃`
          }
        }
      }
      this.chart.clear()
      this.chart.setOption(option)
    },
    disabledDate(time) {
      return time.getTime() > new Date().getTime() || time.getTime() < new Date().getTime() - this.retentionDay * 24 * 60 * 60 * 1000
    },

  }
}
</script>
<style lang="scss" scoped>
.remark {
  position: absolute;
  top: 0;
  right: 20px;
  display: flex;
  justify-content: space-between;
  width: 130px;
  height: 20px;
  // line-height: 50px;
  .remark_item {
    display: flex;
    align-items: center;
    .dot {
      // width: 21px;
      // height: 6px;
      // margin-right: 5px;
      // border-radius: 2px;
      width: 10px;
      height: 10px;
      margin-right: 5px;
      border-radius: 10px;
    }
    .dot1 {
      background-color: #00E667;
    }
    .dot2 {
      background-color: red;
    }
    .text {
      color: black;
      font-family: PingFang SC RE;
      // font-weight: bold;
      font-size: 14px;
    }
  }
}
</style>
