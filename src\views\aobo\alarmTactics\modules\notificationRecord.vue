<template>
  <div class="container">
    <div style="display:flex;justify-content: space-between">
      <div>
        <el-date-picker
          v-model="query.date"
          type="datetimerange"
          value-format="yyyy-MM-dd HH:mm:ss"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          style="margin-right: 20px"
          :default-time="['00:00:00', '23:59:59']"
          @change="getList(1)"
        />
        <el-select
          v-model="query.state"
          placeholder="状态"
          clearable
          @change="getList(1)"
        >
          <el-option
            label="成功"
            :value="1"
          />
          <el-option
            label="失败"
            :value="0"
          />
          <el-option
            label="查询中"
            :value="2"
          />
        </el-select>
      </div>
      <div>
        <el-button
          type="success"
          @click="exportData"
        >
          导出
        </el-button>
      </div>
    </div>
    <!--    表格-->
    <div style="margin-top: 20px">
      <el-table
        v-loading="loading"
        :header-cell-style="tableHeaderStyle"
        header-row-class-name="table-header"
        :data="tableData"
        height="65vh"
        stripe
        row-key="id"
      >
        <el-table-column
          label="序号"
          type="index"
          width="100"
          align="center"
        />
        <el-table-column
          prop="nickName"
          label="通知时间"
          show-overflow-tooltip
          align="center"
        >
          <template slot-scope="scope">
            <span>{{ scope.row.createTime || '--' }}</span>
          </template>
        </el-table-column>
        <el-table-column
          prop="username"
          label="通知人员"
          align="center"
          show-overflow-tooltip
        >
          <template slot-scope="scope">
            <span>{{ scope.row.personNames ? scope.row.personNames.join(', ') : '--' }}</span>
          </template>
        </el-table-column>

        <el-table-column
          prop="isEnable"
          label="通话时长"
          align="center"
          show-overflow-tooltip
        >
          <template slot-scope="scope">
            <span>{{ scope.row.callDuration || '--' }}s</span>
          </template>
        </el-table-column>
        <el-table-column
          prop="sex"
          label="状态"
          align="center"
          show-overflow-tooltip
        >
          <template slot-scope="scope">
            <span
              v-if="scope.row.state === 0"
              style="color: #F74D4D"
            >失败</span>
            <span
              v-if="scope.row.state === 1"
              style="color: #0062FD"
            >成功</span>
            <span v-if="scope.row.state === 2">查询中</span>

          </template>
        </el-table-column>
      </el-table>
      <el-pagination
        background
        :current-page.sync="pageNum"
        :page-size="pageSize"
        layout="total,prev, pager, next,sizes, jumper"
        :page-sizes="[10, 20, 50, 100]"
        :total="total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import { doExport, doPage } from '@/api/aobo/notificationRecord'
import { utf8to16 } from '@/utils'

export default {
  name: 'BaseConfig',
  data() {
    return {
      pageNum: 1,
      pageSize: 10,
      total: 0,
      dialogVisible: false,
      tableData: [], // 列表数据
      loading: false,
      keyword: '',
      query: {
        state: null
      }
    }
  },
  computed: {
    ...mapGetters([
      'tableHeaderStyle', 'btnAuthority'
    ])
  },
  created() {
    this.getList()
  },
  methods: {
    getList(isPage) {
      if (isPage) {
        this.pageNum = 1
        this.selectList = []
      }
      const { query } = this
      const params = {
        pageNum: this.pageNum,
        pageSize: this.pageSize,
        keyword: this.keyword,
        query: {
          ...query,
          startTime: query.date ? query.date[0] : null,
          endTime: query.date ? query.date[1] : null
        }
      }
      this.loading = true
      doPage(params).then((res) => {
        this.tableData = res.data.records.map((item) => item)
        this.pageNum = res.data.current
        this.total = Number(res.data.total)
      }).finally(() => {
        this.loading = false
      })
    },
    exportData() {
      const { query } = this
      doExport({
        ...query,
        startTime: query.date ? query.date[0] : null,
        endTime: query.date ? query.date[1] : null
      }).then((res) => {
        const url = window.URL.createObjectURL(
          new Blob([res.data], {
            type: 'application/vnd.ms-excel;charset=UTF-8'
          })
        )
        const temp = res.headers['content-disposition']
          .split(';')[1]
          .split('filename=')[1]
        const index = temp.indexOf('.')
        const str = temp.substr(0, index)
        const fileName = `${utf8to16(unescape(str))}.xlsx`
        const link = document.createElement('a')
        link.href = url
        link.download = fileName
        link.click()
        this.$message({
          type: 'success',
          message: '导出成功'
        })
      })
    },
    // 分页
    handleSizeChange(val) {
      console.log(`每页 ${val} 条`)
      this.pageSize = val
      this.getList()
    },
    handleCurrentChange(val) {
      this.pageNum = val
      this.getList()
      console.log(this.pageNum)
    }
  }
}
</script>

<style scoped lang="scss">
.container {
  margin-top: 20px;
}
</style>
