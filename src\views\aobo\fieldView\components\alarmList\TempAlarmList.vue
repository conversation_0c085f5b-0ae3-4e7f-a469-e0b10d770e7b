<template>
  <div
    v-loading="loading" class="table_list"
    element-loading-background="rgba(0, 0, 0, 0)"
  >
    <div class="wrapper">
      <header class="header">
        <el-button
          type="primary"
          size="mini"
          style="border-radius: 20px !important"
          :disabled="!btnAuthorityList.includes('dispose') || !list.length"
          @click="showAllDialog"
        >
          一键处置所有
        </el-button>
      </header>
      <div class="container">
        <div
          v-for="item in list"
          :key="item.id"
          class="list_item"
          @click="detail(item)"
        >
          <div class="list_item_title">
            <div class="name">
              <span>{{ item.segmentName }}</span>
            </div>
            <div :style="{color: alarmColorMap[item.level]}">{{ `${item.tempVal / 100}℃` }}</div>
          </div>
          <div class="list_item_detail">
            <el-tooltip :content="item.spaceSerialNum">
              <div style="font-size: 13px; color: #c4ddff;">监控段编号：{{ item.spaceSerialNum }}</div>
            </el-tooltip>

            <div>阈值：{{ `${(item.thresholdVal / 100).toFixed(2)}℃` }}</div>
          </div>

          <div class="list_item_bottom">
            <div class="time">
              <img src="@/assets/page/<EMAIL>">
              <div>{{ item.alarmTime }}</div>
            </div>
            <img src="@/assets/page/dingwei.png">
            <el-button
              type="primary"
              round
              size="mini"
              :disabled="!btnAuthorityList.includes('dispose')"
              @click.stop="handleAlarm(item)"
            >处置</el-button>
          </div>
        </div>
        <div v-if="!list.length" style="display: grid;place-items: center;height: 100%;color: white">暂无数据</div>

      </div>
      <div class="pagination" style="margin-top: 10px!important;">
        <el-pagination
          background
          :pager-count="3"
          :current-page.sync="pageNum"
          :page-size="pageSize"
          layout="total, prev, pager, next"
          :total="total"
          @current-change="handleCurrentChange"
        />
      </div>
      <!-- 报警处置 -->
      <el-dialog
        title="报警处置"
        :visible.sync="dialogVisible"
        width="23vw"
        :modal-append-to-body="false"
        top="30vh"
        @close="closeDialog()"
      >

        <div class="handle-detail">
          <span class="label">处置人：</span>
          <el-select
            v-model="disposer"
            class="input"
          >
            <el-option
              v-for="item in userList"
              :key="item.id"
              :label="item.name"
              :value="item.name"
            />
          </el-select>
        </div>

        <div class="handle-detail">
          <span class="label">处置详情：</span>
          <el-input
            v-model="desc"
            class="input"
            type="textarea"
            resize="none"
          />
        </div>

        <div style="display: flex; align-items: center; padding-left: 50px;margin-top: 12px;">
          <el-input-number
            v-model="alarmNum"
            controls-position="right"
            :min="1"
          />
          <span
            style="margin-left: 10px; font-weight: bold"
          >分钟内不再次报警</span>
        </div>
        <div
          slot="footer"
          class="dialog_footer"
        >
          <el-button
            type="primary"
            plain
            style="margin-right: 10px"
            @click="closeDialog()"
          >取 消</el-button>
          <el-button
            :loading="submitting"
            type="primary"
            @click="handleAlarmSubmit()"
          >确 定</el-button>
        </div>
      </el-dialog>

      <!-- 一键报警处置 -->
      <el-dialog
        v-loading="loading"
        title="报警处置"
        :visible.sync="dialogVisibleAll"
        width="23vw"
        :modal-append-to-body="false"
        top="30vh"
        @close="closeAllDialog"
      >
        <div
          style="
          display: flex;
          align-items: center;
          padding-left: 50px;
          margin: 20px 0 30px;
        "
        >
          <span>批量处理报警条数：</span>
          <span style="color: green; font-weight: bold; margin: 0 5px">{{
            total
          }}</span>
          <span>条</span>
        </div>

        <div class="handle-detail">
          <span class="label">处置人：</span>
          <el-select
            v-model="disposer"
            class="input"
          >
            <el-option
              v-for="item in userList"
              :key="item.id"
              :label="item.name"
              :value="item.name"
            />
          </el-select>
        </div>

        <div class="handle-detail">
          <span class="label">处置详情：</span>
          <el-input
            v-model="desc"
            class="input"
            type="textarea"
            resize="none"
          />
        </div>

        <div style="display: flex; align-items: center; padding-left: 50px; margin-top: 12px;">
          <el-input-number
            v-model="alarmNum"
            controls-position="right"
            :min="0"
          />
          <span
            style="margin-left: 10px;"
          >分钟内不再次报警</span>
        </div>

        <div
          slot="footer"
          class="dialog_footer"
        >
          <el-button
            type="primary"
            plain
            style="margin-right: 10px"
            @click="closeAllDialog"
          >取 消</el-button>
          <el-button
            :loading="submitting"
            type="primary"
            @click="handleAll"
          >确 定</el-button>
        </div>
      </el-dialog>
    </div>

  </div>

</template>

<script>
import { disposeAllAlarm, segmentAlarmPage } from '@/api/sceneView'
import { dropdownList as userPage } from '@/api/aobo/userManagement'
import { alarmColorMap } from '@/constants'

export default {
  name: 'AlarmBattery',
  props: {
    timeInterval: {
      type: Number,
      default: 30
    },
    btnAuthorityList: {
      type: Array,
      default() {
        return []
      }
    },
    serialNum: {
      type: String,
      default: ''
    },
  },
  data() {
    return {
      list: [],
      pageNum: 1,
      pageSize: 4,
      total: 0,
      loading: false,
      submitting: false,
      intervalPage: null, // 列表刷新数据
      dialogVisible: false,
      dialogVisibleAll: false,
      select: {},
      alarmNum: 1, // 几分钟内不再报警
      desc: null, // 处置详情
      userList: [], // 用户列表
      disposer: null, // 处置人
      isAlarm: false,
      showAlarm: true,
      alarmColorMap,
    }
  },
  created() {
    this.doPage(true)
    this.getUserList()
  },
  beforeDestroy() {
    if (this.intervalPage) clearInterval(this.intervalPage)
  },
  methods: {
    doPage(isPage) {
      if (isPage) this.pageNum = 1
      const params = {
        pageNum: this.pageNum,
        pageSize: this.pageSize,
        query: {
          status: 0,
          parentSerialNum: this.serialNum
        }
      }
      this.loading = true
      segmentAlarmPage(params)
        .then((res) => {
          this.isAlarm = !!res.data.records.length
          this.$emit('isAlarm', this.isAlarm, res.data.total)
          this.list = res.data.records
          this.pageNum = res.data.current
          this.total = Number(res.data.total)
        })
        .finally(() => {
          this.loading = false
        })
    },

    /**
     * 获取用户列表
     * */
    getUserList() {
      userPage()
        .then((res) => {
          this.userList = res.data
        })
    },
    detail(row) {
      this.$emit('detail', row)
    },
    // 报警处置
    handleAlarm(e) {
      this.dialogVisible = true
      this.select = e
    },
    showAllDialog() {
      this.dialogVisibleAll = true
    },
    // 报警处置提交
    handleAlarmSubmit() {
      if (!this.alarmNum) {
        this.$message.warning('请输入时间')
        return
      }
      this.submitting = true

      const data = {
        serialNum: this.select.spaceSerialNum,
        intervalMinute: this.alarmNum,
        desc: this.desc,
        disposer: this.disposer
      }
      // disposeAlarm(data)
      disposeAllAlarm(data)
        .then((res) => {
          this.$message.success('成功')
          this.doPage(true)
          this.closeDialog()
        })
        .finally(() => {
          this.submitting = false
        })
    },
    /**
     * 一键处置所有报警
     * */
    handleAll() {
      this.submitting = true
      const data = {
        serialNum: this.serialNum,
        intervalMinute: this.alarmNum,
        desc: this.desc,
        disposer: this.disposer
      }
      disposeAllAlarm(data)
        .then((res) => {
          this.$message.success('成功')
          this.isAlarm = false
          this.$emit('isAlarm', this.isAlarm, 0)
          this.doPage(true)
          this.dialogVisibleAll = false
        })
        .finally(() => {
          this.submitting = false
        })
    },
    closeDialog() {
      this.alarmNum = 1
      this.dialogVisible = false
      this.select = {}
      this.disposer = null
      this.desc = null
    },
    closeAllDialog() {
      this.alarmNum = 1
      this.dialogVisibleAll = false
      this.disposer = null
      this.desc = null
    },
    // 分页
    handleCurrentChange(val) {
      this.pageNum = val
      if (val === 1) {
        this.intervalPage = setInterval(() => {
          this.doPage(true)
        }, Number(this.timeInterval) * 1000)
      } else if (this.intervalPage) clearInterval(this.intervalPage)
      this.doPage()
    }
  }
}
</script>

<style scoped lang="scss">
.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-bottom: 10px;
  .left {
    display: flex;
    align-items: center;
    user-select: none;
    white-space: nowrap;
  }
  .name {
    color: #0069f9;
  }
  .room {
    width: 50px;
    overflow: hidden;
    text-overflow: ellipsis;
  }
  .text {
    color: #81b6ff;
  }
  img {
    width: 19px;
    height: 19px;
    cursor: pointer;
    margin-right: 10px;
  }
}
.wrapper {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.container {
  height: calc(100% - 170px);
  overflow-y: auto;
  overflow-x: hidden;
  scrollbar-width: none;
}
.list_item {
  padding: 0 15px;
  width: 320px;
  height: 155px;
  background: rgba(27, 95, 225, 0.3);
  border-radius: 8px;
  margin-bottom: 15px;
  // font-weight: bold;
  cursor: pointer;
  .list_item_title {
    height: 40px;
    line-height: 40px;
    margin-bottom: 5px;
    border-bottom: 1px solid rgba(142, 216, 246, 0.3);
    display: flex;
    justify-content: space-between;
    font-size: 15px;
    font-weight: bold;
    .name {
      color: #fff;
    }
    .temperature {
      color: #ff511c;
    }
  }
  .list_item_detail {
    font-size: 13px;
    color: #c4ddff;
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    line-height: 30px;
    margin-bottom: 5px;
    div {
      &:nth-child(n) {
        width: 58%;
      }
      &:nth-child(2n) {
        width: 40%;
      }
      text-overflow: ellipsis;
      overflow: hidden;
      white-space: nowrap;
    }
  }
  .list_item_bottom {
    display: flex;
    align-items: center;
    justify-content: space-between;
    .time {
      display: flex;
      align-items: center;
      img {
        width: 15px;
        height: 15px;
        margin-right: 10px;
      }
      div {
        color: #91a0b4;
        font-size: 14px;
        font-weight: bold;
        display: flex;
      }
    }
    img {
      width: 20px;
      height: 28px;
      cursor: pointer;
    }
  }
}

.handle-detail {
  display: flex;
  align-items: center;
  margin-top: 10px;
  padding-left: 50px;
  .label {
    flex-shrink: 0;
    width: 80px;
    text-align: end;
  }
  .input {
    width: 250px;
  }
}

</style>
