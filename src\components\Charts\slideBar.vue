<template>
  <div
    :id="id"
    :style="{ height: height, width: width }"
  />
</template>

<script>
import elementResizeDetectorMaker from 'element-resize-detector'
import chartMixins from './mixins/index'

export default {
  name: 'Dashboard',
  mixins: [chartMixins],
  props: {
    id: {
      require: true,
      type: String,
      default: 'charts'
    },
    width: {
      require: false,
      type: String,
      default: '100%'
    },
    height: {
      require: false,
      type: String,
      default: '100%'
    },
    propData: {
      require: false,
      type: Object,
      default: () => {}
    }
  },
  watch: {
  },
  mounted() {
    this.$nextTick(() => {
      this.initChart()
    })
    const erd = elementResizeDetectorMaker()
    erd.listenTo(document.getElementById(this.id), (element) => {
      const width = element.offsetWidth
      const height = element.offsetHeight
      this.$nextTick(() => {
        console.log(`Size: ${width}x${height}`)
        // 使echarts尺寸重置
        this.chart = this.$echarts.init(document.getElementById(this.id)).resize()
      })
    })
  },
  methods: {
    initChart() {
      this.chart = this.$echarts.init(document.getElementById(this.id))
      // const colors = ['#3A85D3', '#5AD8A6', '#5470c6', '#008000', '#61a0a8']
      this.option = {
        tooltip: {
          trigger: 'axis', // 触发类型；轴触发，axis则鼠标hover到一条柱状图显示全部数据，item则鼠标hover到折线点显示相应数据，
          axisPointer: { // 坐标轴指示器，坐标轴触发有效
            type: 'shadow' // 默认为直线，可选为：'line' | 'shadow'
          }
        },
        xAxis: {
          type: 'category',
          data: this.propData.name
        },
        yAxis: {
          type: 'value'
        },
        grid: {
          left: '8%',
          right: '3%',
          top: '5%',
          bottom: '15%',
          containLabel: false
        },
        series: [
          {
            // itemStyle: {
            //   normal: {
            //     color: '#4694EC'
            //   }
            // },
            data: this.propData.data,
            type: 'bar',
            name: '产值',
            barWidth: 16
          }
        ],
        dataZoom: {
          orient: 'horizontal',
          show: true,
          start: 0,
          end: 41,
          height: 12,
          bottom: 6
        }
      }
      this.chart.setOption(this.option, true)
    }
  }
}
</script>

<style>

</style>

