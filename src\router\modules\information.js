import Layout from '@/layout'

export default {
  path: '/information',
  component: Layout,
  redirect: '/information',
  meta: {
    title: '信息管理',
    icon: 'dashboard',
    code: 'imformation'
  },
  children: [
    {
      path: 'baseInfo',
      name: 'baseInfo',
      component: () => import('@/views/information/baseinfo/index'),
      meta: {
        title: '园区基础信息',
        icon: '',
        code: 'imformation:basic'
      }
    },
    {
      path: 'committee',
      name: 'committee',
      component: () => import('@/views/information/committee/index'),
      meta: {
        title: '园区管委会信息',
        icon: '',
        code: 'imformation:committee'
      }
    },
    {
      path: 'operation',
      name: 'operation',
      component: () => import('@/views/information/operation/index'),
      meta: {
        title: '运行信息管理',
        icon: '',
        code: 'imformation:run'
      }
    }
  ]
}
