
/**
 * 用于替换原生setInterval
 * @param fn 函数
 * @param interval ms 执行间隔
 * @return 函数 取消定时器的函数
 * */
export function useRafInterval(fn, interval = 1000) {
  let nowTime = performance.now()
  let preTime
  let timeDifferance = 0 // 每次的时间偏差
  let timer
  function refresh() {
    preTime ??= performance.now()
    if (preTime) {
      nowTime = performance.now()
      const realInterval = interval - timeDifferance
      if (nowTime - preTime >= realInterval) {
        timeDifferance = (nowTime - preTime - realInterval) % realInterval
        preTime = performance.now()
        fn()
      }
    }

    timer = requestAnimationFrame(refresh)
  }

  timer = requestAnimationFrame(refresh)
  // 立即执行
  fn()
  function cancelTimer() {
    cancelAnimationFrame(timer)
  }

  return cancelTimer
}
