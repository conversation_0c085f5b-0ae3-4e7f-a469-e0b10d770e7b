<template>
  <div
    class="login"
  >
    <div
      ref="loginRef"
      class="login-container"
    >
      <div class="logoBox">
        <img
          :src="$store.state.settings.logo"
          style=""
        >
        <span>{{ $store.state.settings.webTitle }}</span>
      </div>
      <div class="form">
        <el-form
          ref="loginForm"
          :model="loginForm"
          :rules="loginRules"
          class="login-form"
          auto-complete="on"
          label-position="left"
        >

          <div class="title-container">
            <h3 class="title">{{ $store.state.settings.webTitle }}</h3>
            <div class="title_line" />
          </div>
          <el-form-item
            prop="username"
            style="margin-top:60px"
          >
            <span class="svg-container">
              <svg-icon
                icon-class="user"
                style="width:20px;height:20px"
              />
            </span>
            <div class="lineOne" />
            <el-input
              ref="username"
              v-model="loginForm.username"
              placeholder="请输入账号"
              name="username"
              type="text"
              tabindex="1"
              auto-complete="on"
            />
          </el-form-item>
          <el-tooltip
            v-model="capsTooltip"
            content="大写锁定已开"
            placement="right"
            manual
          >
            <el-form-item prop="password">
              <span class="svg-container">
                <svg-icon
                  icon-class="password"
                  style="width:20px;height:20px"
                />
              </span>
              <div class="lineOne" />

              <el-input
                :key="passwordType"
                ref="password"
                v-model="loginForm.password"
                :type="passwordType"
                placeholder="请输入密码"
                name="password"
                tabindex="2"
                auto-complete="on"
                @keyup.native="checkCapslock"
                @keyup.enter.native="handleLogin"
              />
              <span
                class="show-pwd"
                @click="showPwd"
              >
                <svg-icon :icon-class="passwordType === 'password' ? 'eye' : 'eye-open'" />
              </span>
            </el-form-item>
          </el-tooltip>
          <el-form-item
            v-if="securityCode"
            class="login-code"
            prop="code"
          >
            <span class="svg-container">
              <svg-icon
                icon-class="yanzhengma"
                style="width:20px;height:20px"
              />
            </span>
            <el-input
              ref="code"
              v-model="loginForm.code"
              name="code"
              maxlength="12"
              placeholder="请输入验证码"
              class="md-input"
              style="margin-right: 5px;"
              @blur="capsTooltip = false"
              @keyup.enter.native="handleLogin"
            />
            <el-image
              :src="`data:image/jpg;base64,${base64Code}`"
              class="loginForm-code"
              style="width: 79px;height: 33px;"
              fit="contain"
              alt="正在加载"
              @click="getValidateCode"
            />
          </el-form-item>

          <!-- <div class="pwdBox">
            <el-checkbox v-model="rememberPwd">记住密码</el-checkbox>
            <div @click="forgotPassword()">忘记密码？</div>
          </div> -->

          <el-button
            :loading="loading"
            type="primary"
            class="btn"
            style="background: linear-gradient(90deg, #007AFF 0%, #0827C1 100%);"
            @click.native.prevent="handleLogin"
          >登  录</el-button>
        </el-form>
      </div>
    </div>
  </div>
</template>

<script>
// import { validUsername } from '@/utils/validate'
import Cookies from 'js-cookie'
import { getValidateCode } from '@/api/user'
import store from '@/store'
import router, { resetRouter } from '@/router/index'

export default {
  name: 'Login',
  data() {
    // const validateUsername = (rule, value, callback) => {
    //   if (!value) {
    //     callback(new Error('用户名不能为空'))
    //   } else {
    //     callback()
    //   }
    // }
    const validatePassword = (rule, value, callback) => {
      if (value.length < 6) {
        callback(new Error('密码应为不小于6位'))
      } else {
        callback()
      }
    }
    const validateCode = (rule, value, callback) => {
      if (value.length < 1) {
        callback(new Error('请输入验证码'))
      } else {
        callback()
      }
    }
    return {
      loginForm: {
        username: '',
        password: '',
        code: ''
      },
      loginRules: {
        username: [{ required: true, message: '请输入用户名', trigger: 'blur' },
          { pattern: /^\w+$/, message: '请输入数字或字母', trigger: 'blur' }],
        password: [{ required: true, trigger: 'blur', validator: validatePassword }],
        code: [{ required: true, trigger: 'blur', validator: validateCode }]
      },
      capsTooltip: false,
      loading: false,
      passwordType: 'password',
      redirect: undefined,
      checked: false,
      rememberPwd: false,
      securityCode: false,
      isSave: false,
      base64Code: ''
    }
  },
  watch: {
    $route: {
      handler(route) {
        this.redirect = route.query && route.query.redirect
      },
      immediate: true
    },
    '$store.state.settings.loginBg': {
      handler(val) {
        if (this.$refs.loginRef) {
          this.$refs.loginRef.style.backgroundImage = `url(${this.$store.state.settings.loginBg})`
        }
      },
      immediate: true
    }
  },
  mounted() {
    // this.clear()
    // this.getValidateCode()
    this.$refs.loginRef.style.backgroundImage = `url(${this.$store.state.settings.loginBg})`
    this.loginForm.username = Cookies.get('username') || ''
    this.loginForm.password = Cookies.get('password') || ''
    console.log(Cookies.get('username'), Cookies.get('password'))
    if (this.loginForm.username === '') {
      this.loginForm.password = ''
    }
    if (this.loginForm.username === '') {
      this.$refs.username.focus()
    } else if (this.loginForm.password === '') {
      this.$refs.password.focus()
    } else {
      this.rememberPwd = true
    }
  },
  methods: {
    // clear() { clearCode().then() },
    checkCapslock({ shiftKey, key } = {}) {
      if (key && key.length === 1) {
        if ((shiftKey && (key >= 'a' && key <= 'z')) || (!shiftKey && (key >= 'A' && key <= 'Z'))) {
          this.capsTooltip = true
        } else {
          this.capsTooltip = false
        }
      }
      if (key === 'CapsLock' && this.capsTooltip === true) {
        this.capsTooltip = false
      }
    },
    showPwd() {
      if (this.passwordType === 'password') {
        this.passwordType = ''
      } else {
        this.passwordType = 'password'
      }
      this.$nextTick(() => {
        this.$refs.password.focus()
      })
    },
    getValidateCode() {
      getValidateCode(new Date().getTime()).then((res) => {
        this.base64Code = res.data
      })
    },
    handleLogin() {
      this.$refs.loginForm.validate((valid) => {
        if (valid) {
          if (this.rememberPwd) {
            Cookies.set('username', this.loginForm.username)
            Cookies.set('password', this.loginForm.password)
          } else {
            Cookies.set('username', '')
            Cookies.set('password', '')
          }
          this.loading = true
          this.$store.dispatch('user/login', this.loginForm).then((res) => {
            if (res.code === 200) {
              this.$router.push('/')
            } else if (res.code === 422 || res.code === 423) { // 验证码失效
              this.securityCode = true
              this.getValidateCode()
              console.log('验证码失效')
            } else if (res.code === 3001) { // 3次账号密码错误输入验证码
              this.securityCode = true
              this.getValidateCode()
              console.log('3次账号密码错误输入验证码')
            }
            this.loading = false
          }).catch((err) => {
            console.log(err)
            this.loading = false
          })
        } else {
          console.log('error submit!!')
          return false
        }
        return false
      })
    },
    // 忘记密码
    forgotPassword() {
      // this.$router.push('/404')
      // this.$router.push('/')
      store.dispatch('user/resetToken').then(() => {
        store.commit('permission/HAS_FILTER_ROUTES', false)
        resetRouter()
        router.push(`/forgotPassword`)
      })
    }
  }
}
</script>

<style lang="scss">
/* 修复input 背景不协调 和光标变色 */
/* Detail see https://github.com/PanJiaChen/vue-element-admin/pull/927 */

$bg:#283443;
$light_gray:#fff;
$cursor: #1D78F4;

@supports (-webkit-mask: none) and (not (cater-color: $cursor)) {
  .login-container .el-input input {
    color: $cursor;
  }
}

/* reset element-ui css */
.login-container {
  .el-input {
    display: inline-block;
    height: 47px;
    // width: 85%;
    width: 85%;

    input {
      background: transparent;
      border: 0px;
      -webkit-appearance: none;
      border-radius: 0px;
      padding: 12px 5px 12px 15px;
      box-sizing: border-box;
      height: 47px;
      caret-color: $cursor;
      font-size: 18px;
      color: #101010;
      line-height: 47px;

      // &:-webkit-autofill {
      //   box-shadow: 0 0 0px 1000px $bg inset !important;
      //   -webkit-text-fill-color: $cursor !important;
      // }
    }
  }
  .loginForm-code {
    position: absolute;
    right: 18px;
    top: 14px;
  }
  .el-form-item {
    width: 100%;
    height: 60px;
    background: #FFFFFF;
    // border: 1px solid #E6E6E6;
    border-radius: 12px;
    margin: 30px auto;
    background-image: url('../../assets/login/<EMAIL>');
    background-size: 100% 100%;
    .el-form-item__content{
      line-height: 60px;
    }
  }
}
</style>

<style lang="scss" scoped>
$bg:#fff;
$dark_gray:#889aa4;
$light_gray:#eee;
.login {
  position: relative;
  background-color: #fff;
  height: 1080px !important;
  width: 1920px !important;
  overflow: hidden;

}

.login-container {
  height: 1080px;
  width: 1920px;
  position: relative;

  //background: url('../../assets/login/<EMAIL>') no-repeat;
  background-repeat: no-repeat;
  background-size: 100% 100%;
  // opacity: 0.9;
  .topLogin{
    width: 490px;
    height: 110px;
    margin-left: 60px;
    // margin-top: 52px;
    padding-top: 45px;
    img{
      width: 100%;
      height: 100%;
    }
  }
  .form{
    width: 536px;
    height: 100%;
    position: fixed;
    top: 0;
    right: 250px;
    display: flex;
    align-items: center;
    .login-form {
      position: relative;
      // width: 884px;
      // max-width: 100%;
      // padding: 80px 35px 0;
      // margin: 0 auto;
      width: 536px;
      height: 688px;
      // max-width: 100%;
      padding: 80px 78px 0;
      box-sizing: border-box;
      // margin: 0 auto;
      overflow: hidden;
      background-image: url('../../assets/login/bg@2x(1).png');
      background-repeat: no-repeat;
      background-size: 100% 100%;
      .btn {
            margin: 60px 0 60px;
            // width: 333px;
            // height: 66px;
            // margin-left:38px;
            // background: #0581FF;
            // border-radius: 12px;
            // font-size: 20px !important;
            // font-family: PingFang SC;
            // font-weight: 400;
            // color: #FFFFFF;
            width: 380px;
            height: 60px;
            background: #1071E2;
            box-shadow: 0px 5px 10px 1px rgba(11,136,251,0.25);
            border-radius: 2px 2px 2px 2px;
            font-size: 18px !important;
            font-family: PingFang SC, PingFang SC;
            font-weight: 500;
            color: #FFFFFF;
            text-align: center;
            line-height: 30px;
      }
    }
  }

  .tips {
    font-size: 14px;
    color: #fff;
    margin-bottom: 10px;

    span {
      &:first-of-type {
        margin-right: 16px;
      }
    }
  }

  .svg-container {
    // padding: 2px 5px 6px 7px;
    // color: $dark_gray;
    vertical-align: middle;
    width: 50px;
    text-align: center;
    display: inline-block;
  }

  .title-container {
    position: relative;
    .title {
     font-size: 26px;
    font-family: PingFang SC, PingFang SC;
    font-weight: 600;
    color: #303030;
    line-height: 0px;
    // letter-spacing: 20px;
    }
    .title_line {
      width: 53px;
      height: 5px;
      background: linear-gradient(90deg, #0179FB 0%, #245BD6 100%);
    }
  }

  .show-pwd {
    position: absolute;
    right: 10px;
    top: 7px;
    font-size: 16px;
    // color: $dark_gray;
    cursor: pointer;
    user-select: none;
  }
  .text {
    position: absolute;
    bottom: 30px;
    left: 42%;
    font-size: 20px;
    color: #808080;
  }

  .lineOne{
    position: absolute;
    top: 14px;
    // left: 10px;
    display: inline-block;
    width: 0px;
    height: 30px;
    border: 1px solid #D9D9D9;
    opacity: 0.9;
  }
}
.pwdBox{
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 16px;
  font-family: PingFang SC, PingFang SC;
  font-weight: 400;
  color: #A0A5AA;
}

.logoBox{
  padding-top: 50px;
  margin-left: 50px;
  display: flex;
  align-items: center;
  img{
    height: 80px;
    width: 84px;
  }
  span{
    white-space: nowrap;
    margin-left: 12px;
    font-size: 36px;
    font-family: PingFang SC, PingFang SC;
    font-weight: 600;
    color: black;
    line-height: 0px;
  }
}

</style>
