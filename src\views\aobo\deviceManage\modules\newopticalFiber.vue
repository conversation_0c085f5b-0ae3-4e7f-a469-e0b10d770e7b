<template>
  <div id="newopticalFiber">
    <div class="top">
      <div class="left">
        <img
          src="@/assets/device_manage/<EMAIL>"
          @click="back"
        >
        <div class="pa_name">DTS管理</div>
        <div class="ch_name">/</div>
        <div class="pa_name">光纤管理</div>
        <div class="ch_name">/</div>
        <div class="ch_name">{{ targetData.type === 1 ? '新增':
          targetData.type === 2 ? '详情': '编辑' }}</div>
      </div>
    </div>
    <div class="base_info">
      <div class="title">光纤信息</div>
      <div class="content">
        <el-form
          ref="form"
          :model="formData.baseInfo"
          :rules="rules"
          label-width="190px"
          class="form_part"
        >
          <el-form-item
            label="光纤编号:"
            prop="cableCode"
            class="form_item"
          >
            <el-input
              v-model="formData.baseInfo.cableCode"
              placeholder="请输入"
              size="large"
              style="width:250px;"
              :disabled="targetData.type === 2"
            />
          </el-form-item>
          <el-form-item
            label="光纤名称:"
            prop="name"
            class="form_item"
          >
            <el-input
              v-model="formData.baseInfo.name"
              placeholder="请输入"
              size="large"
              style="width:250px;"
              :disabled="targetData.type === 2"
            />
          </el-form-item>
          <!--          <el-form-item-->
          <!--            label="采样率:"-->
          <!--            prop="resolutionRatio"-->
          <!--            class="form_item"-->
          <!--          >-->
          <!--            <el-input-->
          <!--              v-model="formData.baseInfo.resolutionRatio"-->
          <!--              placeholder="请输入"-->
          <!--              size="large"-->
          <!--              style="width:250px;"-->
          <!--              :disabled="targetData.type === 2 || !isResolutionEdit"-->
          <!--            />-->
          <!--            <span>m</span>-->
          <!--          </el-form-item>-->
          <el-form-item
            label="光缆长度:"
            prop="cableLength"
          >
            <el-input
              v-model="formData.baseInfo.cableLength"
              placeholder="请输入"
              size="large"
              style="width:250px;"
              :disabled="targetData.type === 2"
            />
            <span>m</span>
          </el-form-item>
          <el-form-item
            label="纤芯类型:"
            prop="fibreCoreSize"
            class="form_item"
          >
            <el-input
              v-model="formData.baseInfo.fibreCoreSize"
              placeholder="请输入"
              size="large"
              style="width:250px;"
              :disabled="targetData.type === 2"
            />
          </el-form-item>
          <!--          <el-form-item-->
          <!--            label="初始温度:"-->
          <!--            prop="defaultTemp"-->
          <!--            class="form_item"-->
          <!--          >-->
          <!--            <el-input-number-->
          <!--              v-model="formData.baseInfo.defaultTemp"-->
          <!--              placeholder="请输入"-->
          <!--              size="large"-->
          <!--              style="width:250px;"-->
          <!--              :controls="false"-->
          <!--              :disabled="targetData.type === 2"-->
          <!--            />-->
          <!--            <span>℃</span>-->
          <!--          </el-form-item>-->
          <el-form-item
            label="采样率:"
            prop="resolutionRatio"
            class="form_item"
          >
            <el-input-number
              v-model="formData.baseInfo.resolutionRatio"
              placeholder="请输入"
              size="large"
              style="width:250px;"
              :min="0"
              :step="0.1"
              :controls="false"
              :disabled="targetData.type === 2"
            />
          </el-form-item>
          <el-form-item
            label="历史数据保存间隔:"
            prop="resolutionRatio"
          >
            <el-input-number
              v-model="formData.baseInfo.historyInterval"
              placeholder="请输入"
              size="large"
              style="width:250px;"
              :min="1"
              :step="1"
              :controls="false"
              :disabled="targetData.type === 2"
            />
            <span>秒</span>
          </el-form-item>
        </el-form>
      </div>
    </div>
    <div
      v-if="targetData.type !== 2"
      class="center_btn"
    >
      <el-button
        type="primary"
        @click="saveBaseInfo"
      >确定</el-button>
    </div>
    <div class="table_list">
      <div class="title">分段设置</div>
      <div class="content">
        <div
          v-if="targetData.type !== 2"
          class="list_item"
        >
          <div class="item_info">
            <div class="input_style">
              <div class="text">起始点：</div>
              <el-input
                v-model="startPosition"
                placeholder="请输入"
                size="large"
                style="width:250px;"
              />
              <div class="text">m</div>
            </div>
            <div class="input_style">
              <div class="text">终点：</div>
              <el-input
                v-model="endPosition"
                placeholder="请输入"
                size="large"
                style="width:250px;"
              />
              <div class="text">m</div>
            </div>
            <div class="input_style">
              <div class="text">名称：</div>
              <el-input
                v-model="name"
                placeholder="请输入"
                size="large"
                style="width:250px;"
              />
            </div>
          </div>
          <div class="btn_list">
            <el-button
              type="primary"
              @click="addParagraph"
            >新增</el-button>
            <el-button
              type="danger"
              @click="clearParagraph"
            >清空分段</el-button>
            <el-button
              type="success"
              @click="batchImport"
            >批量导入</el-button>
          </div>
        </div>
        <el-table
          ref="multipleTable"
          :header-cell-style="tableHeaderStyle"
          header-row-class-name="table-header"
          :data="tableData"
          stripe
          :height="targetData.type === 2 ? (innerHeight - 580) : (innerHeight - 720)"
        >
          <el-table-column
            label="监控段"
            type="index"
            width="70"
            align="center"
          />
          <el-table-column
            prop="startPosition"
            label="起始点（m）"
            show-overflow-tooltip
            align="center"
          >
            <template slot-scope="scope">
              <el-input
                v-if="scope.row.edit"
                v-model="scope.row.startPosition"
                placeholder="请输入"
                style="width:200px;"
              />
              <span v-else>{{ (scope.row.startPosition || scope.row.startPosition === 0)
                ? scope.row.startPosition : '--' }}</span>
            </template>
          </el-table-column>
          <el-table-column
            prop="endPosition"
            label="终点（m）"
            align="center"
            show-overflow-tooltip
          >
            <template slot-scope="scope">
              <el-input
                v-if="scope.row.edit"
                v-model="scope.row.endPosition"
                placeholder="请输入"
                style="width:200px;"
              />
              <span v-else>{{ (scope.row.endPosition || scope.row.endPosition === 0)
                ? scope.row.endPosition : '--' }}</span>
            </template>
          </el-table-column>
          <el-table-column
            prop="name"
            label="名称"
            align="center"
            show-overflow-tooltip
          >
            <template slot-scope="scope">
              <el-input
                v-if="scope.row.edit"
                v-model="scope.row.name"
                placeholder="请输入"
                style="width:200px;"
              />
              <span v-else>{{ scope.row.name || '--' }}</span>
            </template>
          </el-table-column>

          <el-table-column
            v-if="targetData.type !== 2"
            label="操作"
            fixed="right"
            align="center"
            width="160"
          >
            <template v-slot="scope">
              <el-button
                v-if="scope.row.edit"
                type="text"
                style="text-decoration:underline;margin-right:15px;"
                @click="saveParagraph(scope.row)"
              >确定</el-button>
              <el-button
                v-else
                type="text"
                style="color:#67C23A;text-decoration:underline;margin-right:15px;"
                @click="editParagraph(scope.row)"
              >编辑</el-button>
              <el-button
                type="text"
                style="color:#F56C6C;text-decoration:underline;"
                @click="delParagraph(scope.row, scope.$index)"
              >删除</el-button>
            </template>
          </el-table-column>
        </el-table>
        <el-pagination
          background
          :current-page.sync="pageNum"
          :page-size="pageSize"
          layout="total,prev, pager, next,sizes, jumper"
          :page-sizes="[10, 20, 50, 100]"
          :total="total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>

    <!-- 删除 -->
    <el-dialog
      title="删除"
      :visible.sync="dialogVisibleDel"
      :modal-append-to-body="false"
      width="500px"
      top="320px"
      @close="closeDelDialog()"
    >
      <div style="display:flex;align-items:center;padding-left:50px">
        <img
          src="@/assets/<EMAIL>"
          style="width:20px;height:20px;margin-right:10px"
        >
        <div style="font-size:16px;color:#F94E4E">
          {{ delText }}
        </div>
      </div>
      <div
        slot="footer"
        class="dialog_footer"
      >
        <el-button @click="closeDelDialog()">取 消</el-button>
        <el-button
          type="primary"
          @click="handleDel()"
        >确 认</el-button>
      </div>
    </el-dialog>

    <!-- 导入 -->
    <el-dialog
      title="导入"
      :visible.sync="importDialogVisible"
      width="fit-content"
      :modal-append-to-body="false"
      top="30vh"
      @close="closeImportDialog()"
    >
      <div style="padding:0 30px 0;">
        <div style="display:flex;justify-content: space-between;margin-bottom: 30px;align-items: center">
          <div
            slot="tip"
            class="uploadText1"
          >选择文件
            <div class="uploadText2">(支持扩展名:xls、xlsx)</div>
          </div>
          <el-link
            type="primary"
            @click="downloadTemplate"
          >点击此处下载模板</el-link>
        </div>
        <el-upload
          class="upload-demo"
          action="#"
          :auto-upload="false"
          :show-file-list="false"
          drag
          :on-change="handleImportSubmit"
        >
          <i
            v-show="false"
            class="el-icon-upload"
          />

          <img
            src="@/assets/userManage/<EMAIL>"
            style="width: 25.8px;height: 24.5px;margin: 17% 0 13px;"
          >
          <div class="el-upload__text"><em>选择上传</em></div>
        </el-upload>
      </div>
    </el-dialog>

    <el-dialog
      :title="resultData.successTotal ? '导入成功' : '导入失败'"
      :visible.sync="dialogVisibleImport"
      width="480px"
      :before-close="handleCloseImport"
      :modal-append-to-body="false"
    >
      <import-result
        :result-data="resultData"
        @handleClose="handleCloseImport"
        @submitForm="submitFormImport"
        @downFail="downFail"
      />
    </el-dialog>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import {
  opticalFiberSave,
  paragraphPage,
  saveParagraph,
  delParagraph,
  clearParagraph,
  downloadParaTemplate,
  importParagraph
} from '@/api/deviceManage'
import { deepClone, utf8to16 } from '@/utils'
import importResult from '@/components/importResult'

export default {
  name: 'NewopticalFiber',
  components: { importResult },
  props: {
    targetData: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      formData: {
        baseInfo: {
          cableCode: '',
          name: '',
          resolutionRatio: '',
          cableLength: '',
          fibreCoreSize: ''
        }
      },
      rules: {
        cableCode: [
          { required: true, message: '请输入光纤编号', trigger: 'blur' }
        ],
        name: [
          { required: true, message: '请输入光纤名称', trigger: 'blur' }
        ],
        resolutionRatio: [
          { required: true, message: '请输入采样率', trigger: 'blur' }
        ],
        cableLength: [
          { required: true, message: '请输入光缆长度', trigger: 'blur' }
        ],
        fibreCoreSize: [
          { required: true, message: '请输入纤芯类型', trigger: 'blur' }
        ]
      },
      startPosition: null, // 起始点
      endPosition: null, // 终点
      name: null, // 名称
      tableData: [],
      pageNum: 1,
      sort: 0, // 排序
      pageSize: 10,
      total: 0,
      deviceId: null, // 父级设备id
      opticalFiberId: null, // 父级光缆id
      editState: false, // 是否有分段数据未保存
      select: {},
      dialogVisibleDel: false,
      delText: '',
      isResolutionEdit: true, // 是否能编辑采样率
      innerHeight: 919,
      importDialogVisible: false,
      resultData: {}, // 导入结果
      dialogVisibleImport: false
    }
  },
  computed: {
    ...mapGetters(['tableHeaderStyle'])
  },
  mounted() {
    // 编辑
    if (this.targetData.data.id) {
      this.isResolutionEdit = false
      this.$set(this.formData.baseInfo, 'id', this.targetData.data.id)
      this.opticalFiberId = this.targetData.data.opticalFiberId
      this.formData.baseInfo = { ...this.targetData.data }
      this.formData.baseInfo.cableCode = this.targetData.data.cableCode
      this.formData.baseInfo.name = this.targetData.data.name
      this.formData.baseInfo.fibreCoreSize = this.targetData.data.fibreCoreSize
      if (this.targetData.data.resolutionRatio) {
        this.formData.baseInfo.resolutionRatio = this.targetData.data.resolutionRatio / 100
      }
      if (this.targetData.data.cableLength) {
        this.formData.baseInfo.cableLength = this.targetData.data.cableLength / 100
      }
      this.getList(1)
    }
    this.deviceId = this.targetData.data.deviceId
    this.$set(this.formData.baseInfo, 'deviceId', this.targetData.data.deviceId)
    window.onresize = () => {
      this.innerHeight = window.innerHeight
    }
  },
  methods: {
    back() {
      this.$emit('changePage', { type: 'opticalFiberManage', data: this.targetData.data.parentData })
    },
    async saveBaseInfo() {
      await this.$refs.form.validate()
      const data = deepClone(this.formData.baseInfo)
      data.resolutionRatio *= 100
      data.cableLength *= 100
      opticalFiberSave(data).then((res) => {
        if (res.code === 200) {
          this.$message.success('成功')
          this.isResolutionEdit = false
          // 若为新增，则存取后端返回的parentId
          if (!this.opticalFiberId && this.opticalFiberId !== 0) this.opticalFiberId = res.data
        }
      })
    },
    // 新增分段
    addParagraph() {
      if (!this.opticalFiberId && this.opticalFiberId !== 0) {
        this.$message.warning('请先保存光纤信息')
        return
      }
      if (!this.startPosition && this.startPosition !== 0) {
        this.$message.warning('请输入起始点')
        return
      }
      if (!this.endPosition) {
        this.$message.warning('请输入终点')
        return
      }
      if (!this.name) {
        this.$message.warning('请输入名称')
        return
      }
      if (this.editState) {
        this.$message.warning('请先保存分段数据')
        return
      }
      // 计算点位数量
      const data = {
        dtsCableId: this.opticalFiberId,
        startPosition: this.startPosition,
        endPosition: this.endPosition,
        name: this.name
      }
      this.saveParagraph(data)
    },
    // 批量导入
    batchImport() {
      if (!this.opticalFiberId && this.opticalFiberId !== 0) {
        this.$message.warning('请先保存光纤信息')
        return
      }
      if (this.editState) {
        this.$message.warning('请先保存分段数据')
        return
      }
      this.importDialogVisible = true
    },
    closeImportDialog() {
      this.importDialogVisible = false
    },
    // 下载模板
    downloadTemplate() {
      downloadParaTemplate().then((res) => {
        const url = window.URL.createObjectURL(
          new Blob([res.data], {
            type: 'application/vnd.ms-excel;charset=UTF-8'
          })
        )
        const temp = res.headers['content-disposition']
          .split(';')[1]
          .split('filename=')[1]
        const index = temp.indexOf('.')
        const str = temp.substr(0, index)
        const fileName = `${utf8to16(unescape(str))}.xlsx`
        const link = document.createElement('a')
        link.href = url
        link.download = fileName
        link.click()
        this.$message({
          type: 'success',
          message: '下载成功'
        })
      })
    },
    // 导入
    handleImportSubmit(file) {
      const blob = file.raw
      const type = blob.name.split('.')[1]
      if (type !== 'xlsx') {
        this.$message.warning(
          '导入文件只能是.xlsx结尾的文件，请检查上传文件是否是从本页面下载的模板'
        )
      } else {
        const loading = this.$loading({
          lock: true,
          text: '上传中',
          spinner: 'el-icon-loading',
          background: 'rgba(0, 0, 0, 0.7)'
        })
        const formData = new FormData()
        formData.append('file', blob)
        importParagraph(formData, this.opticalFiberId)
          .then((res) => {
            this.importDialogVisible = false
            this.resultData = res.data
            this.dialogVisibleImport = true
          }).finally(() => {
            loading.close()
          })
      }
    },
    handleCloseImport() {
      this.getList()
      this.dialogVisibleImport = false
    },
    submitFormImport() {
      this.getList()
      this.dialogVisibleImport = false
    },
    // 下载失败数据
    downFail() {
      const oReq = new XMLHttpRequest()
      oReq.open('GET', this.resultData.failExcelUrl, true)
      oReq.responseType = 'arraybuffer'
      oReq.withCredentials = true
      // eslint-disable-next-line func-names
      oReq.onload = function() {
        const arraybuffer = oReq.response
        if (arraybuffer) {
          const byteBUffer = new Uint8Array(arraybuffer)
          const link = document.createElement('a')
          const blob = new Blob([byteBUffer], {
            type: 'application/vnd.ms-excel;charset=UTF-8'
          })
          link.href = URL.createObjectURL(blob)
          link.download = '导入失败数据.xlsx'
          link.click()
        }
      }
      oReq.send()
      this.$message({
        type: 'success',
        message: '下载成功'
      })
    },
    // 获取dts列表
    getList(isPage) {
      if (isPage) {
        this.pageNum = 1
      }
      const params = {
        pageNum: this.pageNum,
        pageSize: this.pageSize,
        query: {
          sort: this.sort,
          dtsCableId: this.opticalFiberId
        }
      }
      paragraphPage(params).then((res) => {
        this.tableData = res.data.records.map((item) => {
          item.startPosition /= 100
          item.endPosition /= 100
          return item
        })
        this.pageNum = res.data.current
        this.total = Number(res.data.total)
      })
    },
    // 分页
    handleSizeChange(val) {
      this.pageSize = val
      this.editState = false
      this.getList()
    },
    handleCurrentChange(val) {
      this.pageNum = val
      this.editState = false
      this.getList()
    },
    // 编辑分段
    editParagraph(row) {
      if (this.editState) {
        this.$message.warning('请先保存分段数据')
        return
      }
      this.editState = true
      const obj = this.tableData.find((item) => item.id === row.id)
      if (obj) this.$set(obj, 'edit', true)
    },
    // 保存分段
    saveParagraph(row) {
      if (!row.startPosition && row.startPosition !== 0) {
        this.$message.warning('请输入起始点')
        return
      }
      if (!row.endPosition) {
        this.$message.warning('请输入终点')
        return
      }
      // if (Number(row.endPosition) < Number(row.startPosition)) {
      //   this.$message.warning('终点不能小于起点')
      //   return
      // }
      if (!row.name) {
        this.$message.warning('请输入名称')
        return
      }
      const data = {
        id: row.id,
        startPosition: row.startPosition,
        endPosition: row.endPosition,
        name: row.name,
        dtsDeviceId: this.deviceId,
        dtsCableId: this.opticalFiberId
      }
      saveParagraph(data).then((res) => {
        this.$message.success('成功')
        this.editState = false
        this.getList()
      })
    },
    // 清空分段
    clearParagraph() {
      if (!this.opticalFiberId && this.opticalFiberId !== 0) {
        this.$message.warning('请先保存光纤信息')
        return
      }
      this.delText = '确认清空分段数据吗？'
      this.dialogVisibleDel = true
    },
    // 删除分段
    delParagraph(row, index) {
      if (!row.id) {
        this.tableData.splice(index, 1)
        this.editState = false
      } else {
        this.delText = '确认删除所选数据吗？这将同时删除该分段下的监控段'
        this.select = deepClone(row)
        this.dialogVisibleDel = true
      }
    },
    // 删除提交
    handleDel() {
      if (this.select.id) {
        const data = {
          id: this.select.id
        }
        delParagraph(data).then((res) => {
          this.$message.success('成功')
          this.editState = false
          if (this.pageNum > 1 && this.tableData.length === 1) {
            this.pageNum--
          }
          this.getList()
          this.closeDelDialog()
        })
      } else {
        clearParagraph({ cableId: this.opticalFiberId }).then((res) => {
          this.$message.success('成功')
          this.editState = false
          this.getList(1)
          this.closeDelDialog()
        })
      }
    },
    // 删除取消
    closeDelDialog() {
      this.select = {}
      this.dialogVisibleDel = false
    }
  }
}
</script>

<style lang="scss" scoped>
.top {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-bottom: 15px;
  border-bottom: 1px solid #E5E5E5;
  margin-bottom: 25px;
  .left {
    display: flex;
    align-items: center;
    font-size: 17px;
    font-weight: bold;
    font-family: PingFang SC RE;
    img {
      width: 28px;
      height: 28px;
      cursor: pointer;
      margin-right: 10px;
    }
    .pa_name {
      color: #8D95A5;
      margin-right: 5px;
    }
    .ch_name {
      color: #202225;
      margin-right: 5px;
    }
  }
}
.title {
  height: 20px;
  font-size: 17px;
  font-weight: bold;
  color: #202225;
  padding-left: 10px;
  border-left: 4px solid #1768EB;
  margin-bottom: 15px;
}
.base_info {
  font-family: PingFang SC RE;
  .content {
    border: 1px solid #E0E0E0;
    border-radius: 5px;
    padding: 20px 25px;
    padding-bottom: 0px;
    margin-bottom: 25px;
    .form_part {
      display: flex;
      flex-wrap: wrap;
      .form_item {
        width: 25%;
        span {
          font-weight: bold;
          margin-left: 10px;
        }
      }
    }
  }
}
.center_btn {
  display:flex;
  justify-content:flex-end;
  padding-bottom:25px;
  border-bottom: 1px solid #E0E0E0;
  margin-bottom: 25px;
}
.table_list {
  font-family: PingFang SC RE;
  .content {
    border: 1px solid #E0E0E0;
    border-radius: 5px;
    padding: 20px;
    .list_item {
      display: flex;
      justify-content: space-between;
      margin-bottom: 20px;
      .item_info {
        display: flex;
        .input_style {
          display: flex;
          align-items: center;
          margin-right: 30px;
          .text {
            font-family: PingFang SC RE;
            font-weight: bold;
            margin-left: 10px;
          }
        }
      }
      .btn_list {
        display: flex;
      }
    }
  }
}
.table_title {
  display: flex;
  align-items: center;
  justify-content: center;
  .tips {
    width:18px;
    height:18px;
    line-height: 18px;
    font-size: 13px;
    border-radius:18px;
    color:#fff;
    background:#727272;
    margin-left: 8px;
    cursor: pointer;
  }
}
</style>
