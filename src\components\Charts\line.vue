<template>
  <div
    id="charts"
    :style="{width, height}"
  />
</template>

<script>

export default {
  components: {},
  props: {
    width: {
      type: String,
      default: '100%'
    },
    height: {
      type: String,
      default: '300px'
    },
    propData: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      myChart: null,
      options: null
    }
  },

  mounted() {
    this.charts()
    console.log(this.propData, 888)
  },
  methods: {
    charts() {
      this.myChart = this.$echarts.init(document.querySelector('#charts'))
      this.options = {
        color: ['#0061CE'],
        legend: {
          data: ['voc浓度'],
          right: '10%'
          // lineStyle: {
          //   // color: '#fff'
          // }
        },
        xAxis: {
          type: 'category',
          boundaryGap: false,
          axisTick: {
            show: false
          },
          splitLine: {
            show: true,
            lineStyle: {
              type: 'dashed'
            }
          },
          data: this.propData.xData
        },
        yAxis: {
          type: 'value',
          name: '单位/m³',
          min: 0,
          axisLine: {
            show: false
          },
          axisTick: {
            show: false
          },
          splitLine: {
            show: true,
            lineStyle: {
              type: 'dashed'
            }
          }
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'cross',
            label: {
              backgroundColor: '#6a7985'
            }
          }
        },
        series: [{
          type: 'line',
          name: 'voc浓度',
          smooth: true, // 折现圆角
          symbol: 'circle',
          symbolSize: 10,
          lineStyle: {
            normal: {
              color: '#0061CE',
              width: 2
            }
          },
          areaStyle: {
            normal: {
              color: new this.$echarts.graphic.LinearGradient(
                0,
                0,
                0,
                1,
                [{
                  offset: 0,
                  color: 'rgba(0, 97, 206, 0.3)'
                },
                {
                  offset: 1,
                  color: 'rgba(255, 255, 255, 0)'
                }
                ],
                false
              )
            }
          },
          data: this.propData.yData
        }]
      }
      this.myChart.setOption(this.options)
    },
    setOption(myChart) {
      myChart.setOption(this.options)
    }
  }
}
</script>
<style lang="scss" scoped>
#charts{
  width: 100%;
  height: 300px;
}
</style>
