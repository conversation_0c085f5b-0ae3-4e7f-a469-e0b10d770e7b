<template>
  <div
    :id="id"
    :style="{ height: height, width: width }"
  />
</template>
<script>
import elementResizeDetectorMaker from 'element-resize-detector'
import chartsMixIn from './mixins'

export default {
  name: 'EconomicsBar',
  mixins: [chartsMixIn],
  props: {
    id: {
      require: false,
      type: String,
      default: 'charts'
    },
    width: {
      require: false,
      type: String,
      default: '100%'
    },
    height: {
      require: false,
      type: String,
      default: '100%'
    },
    propData: {
      require: false,
      type: Object,
      default: () => {}
    }
  },
  created() {
    this.doNotRedraw = true
  },
  mounted() {
    this.initChart()
    // console.log(this.propData, 888888888)
    const erd = elementResizeDetectorMaker()
    erd.listenTo(document.getElementById(this.id), (element) => {
      const width = element.offsetWidth
      const height = element.offsetHeight
      this.$nextTick(() => {
        console.log(`Size: ${width}x${height}`)
        // 使echarts尺寸重置
        this.chart = this.$echarts.init(document.getElementById(this.id)).resize()
      })
    })
  },
  methods: {
    initChart() {
      this.chart = this.$echarts.init(document.getElementById(this.id))
      this.option = {
        color: ['#6395F9', '#62DAAB', '#657798'],
        title: {
          text: '',
          textStyle: {
            color: 'rgb(0, 0, 0)'
          }
        },
        tooltip: {
          trigger: 'axis'
        },
        grid: [
          { bottom: '20%' },
          { top: '10%' }
        ],
        legend: {
          data: this.propData.XData,
          icon: 'circle',
          bottom: 10,
          left: 'center'
        },
        xAxis: [
          {
            type: 'category',
            data: this.propData.month,
            axisTick: {
              alignWithLabel: true
            }
          }
        ],
        yAxis: [
          {
            type: 'value',
            name: '（个）',
            splitLine: {
              lineStyle: {
                type: 'dashed',
                color: '#E9E9E9'
              }
            },
            minInterval: 1 // 最小刻度
          }
        ],
        series: [
          {
            name: '意向企业',
            type: 'bar',
            barWidth: 10, // 柱图宽度
            data: this.propData.intentionEnterpriseCount
          },
          {
            name: '入驻企业',
            type: 'bar',
            barWidth: 10, // 柱图宽度
            data: this.propData.enterEnterpriseCount
          },
          {
            name: '投资项目',
            type: 'bar',
            barWidth: 10, // 柱图宽度
            data: this.propData.projectCount
          }
        ]
      }
      this.chart.setOption(this.option)
      // this.autoPlayTool(this.chart, this.propData, 0)
    }
  }
}
</script>
<style scoped>
</style>
