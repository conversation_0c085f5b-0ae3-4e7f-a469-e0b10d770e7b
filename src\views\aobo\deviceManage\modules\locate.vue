<template>
  <div v-loading="loading">
    <div class="top">
      <div class="left">
        <img
          src="@/assets/device_manage/<EMAIL>"
          @click="back"
        >
        <div class="pa_name">空间与设备管理</div>
        <div class="ch_name">/</div>
        <div class="ch_name">工作面管理</div>
        <div class="ch_name">/</div>
        <div class="ch_name">定位</div>
      </div>
    </div>
    <div class="content">
      <div
        class="content_left"
      >
        <div class="title">
          <div class="title_name">工作面列表</div>
        </div>
        <div
          class="list"
          :style="{height: innerHeight - 350 + 'px'}"
        >
          <div
            v-for="item in cabList"
            :key="item.name"
            class="list_item"
            draggable="true"
            @dragstart="dragstart(item, $event)"
          >
            <div class="list_item_content">
              <div class="cab_name">{{ item.name }}</div>
              <img
                v-if="!item.tierRow"
                src="@/assets/page/dingwei.png"
                @click="locateByClick(item)"
              >
            </div>
          </div>
        </div>
      </div>
      <div
        :style="{height: innerHeight - 275 + 'px'}"
        class="content_right"
      >
        <div
          v-for="i in row"
          :key="i"
          class="row"
        >
          <div
            v-for="j in col"
            :key="j"
            class="col"
          >
            <div
              v-if="cabMap[`${i}-${j}`]"
              class="owned"
            >
              <img
                src="@/assets/device_manage/<EMAIL>"
                alt=""
                class="owned-img"
                draggable="true"
                @dragstart="dragstart(cabMap[`${i}-${j}`], $event)"
              >
              <div class="name">{{ cabMap[`${i}-${j}`].name }}</div>
              <img
                class="delete-icon"
                draggable="false"
                src="@/assets/device_manage/<EMAIL>"
                @click="delLocate(cabMap[`${i}-${j}`])"
              >
            </div>
            <img
              v-else
              draggable="false"
              src="@/assets/device_manage/<EMAIL>"
              :class="{active: active && active.row === i && active.col === j}"
              @dragenter="dragenter"
              @dragover="dragover"
              @drop="drop({row: i, col:j},$event)"
              @click="mark({row: i, col:j})"
            >
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { deepClone } from '@/utils'
import { workFaceList, saveCabinet } from '@/api/deviceManage'

export default {
  name: 'BatteryLocate',
  props: {
    targetData: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      cabList: [],
      selectCab: {},
      innerHeight: null,
      row: 3,
      col: 100,
      loading: false,
      cabMap: {},
      active: null,
      dragging: false
    }
  },
  mounted() {
    this.getCabList()
    this.innerHeight = window.innerHeight
    window.onresize = () => {
      this.innerHeight = window.innerHeight
    }
  },
  methods: {
    // 获取工作面列表
    getCabList() {
      const params = {
        serialNum: `1${this.targetData.serialNum}`
      }
      this.loading = true
      workFaceList(params).then((res) => {
        this.handleData(res.data)
      }).finally(() => {
        this.loading = false
      })
    },
    handleData(data) {
      // 数据排序，将tierRow为空的放前面
      this.cabList = data.sort((a, b) => {
        if (a.tierRow && !b.tierRow) {
          return 1
        } if (!a.tierRow && b.tierRow) {
          return -1
        }
        return 0
      })
      // 获取最大行数
      // this.row = Math.max(...this.cabList.map((item) => item.tierRow || 0))
      // 获取最大列数
      // this.col = Math.max(...this.cabList.map((item) => item.tierColumn || 0))
      // 将数据处理成map(key是工作面的行列)
      this.cabMap = {}
      for (const item of data) {
        if (!item.tierRow) continue
        const key = `${item.tierRow}-${item.tierColumn}`
        this.cabMap[key] = item
      }
    },
    /**
     * 拖动工作面
     * */
    dragstart(raw, e) {
      this.dragging = true
      this.selectCab = deepClone(raw)
      e.dataTransfer.setData('text/plain', raw.id)
      e.dataTransfer.effectAllowed = 'copy'
    },
    back() {
      this.$emit('changePage', { type: 'batteryCabManage', data: this.targetData })
    },
    dragenter(e) {
      e.preventDefault()
    },
    dragover(e) {
      e.preventDefault()
    },
    /**
     * 通过拖动定位
     * */
    drop({ row, col }, e) {
      e.preventDefault()
      this.dragging = false
      const data = { ...this.selectCab, tierRow: row, tierColumn: col }
      this.active = null

      saveCabinet(data).then((res) => {
        if (res.code === 200) {
          this.$message.success('成功')
          this.getCabList()
        }
      })
    },
    /**
     *  通过点击定位
     * */
    locateByClick(raw) {
      if (!this.active) return
      const { active } = this
      this.active = null
      const data = { ...raw, tierRow: active.row, tierColumn: active.col }

      saveCabinet(data).then((res) => {
        if (res.code === 200) {
          this.$message.success('成功')
          this.getCabList()
        }
      })
    },
    /**
     * 标记当前位置
     * */
    mark({ row, col }) {
      if (this.active && this.active.row === row && this.active.col === col) {
        this.active = null
      } else {
        this.active = { row, col }
      }
    },
    /**
     * 删除定位
     * */
    delLocate(raw) {
      const data = { ...raw, tierRow: null, tierColumn: null }
      saveCabinet(data).then((res) => {
        if (res.code === 200) {
          this.$message.success('成功')
          this.getCabList()
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.top {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-bottom: 15px;
  margin-bottom: 30px;
  border-bottom: 1px solid #E5E5E5;
  .left {
    display: flex;
    align-items: center;
    font-size: 17px;
    font-weight: bold;
    font-family: PingFang SC RE;
    img {
      width: 28px;
      height: 28px;
      cursor: pointer;
      margin-right: 10px;
    }
    .pa_name {
      color: #8D95A5;
      margin-right: 5px;
    }
    .ch_name {
      color: #202225;
      margin-right: 5px;
    }
  }
}
.content {
  display: flex;
  justify-content: space-between;
  font-family: PingFang SC RE;
  .title {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-bottom: 20px;
    border-bottom: 1px solid #E5E5E5;
    .title_name {
      border-left: 4px solid #1768EB;
      padding-left: 10px;
      font-size: 18px;
      font-weight: bold;
      color: #202225;
    }
  }
  .content_left {
    width: 25%;
    border: 1px solid #E5E5E5;
    border-radius: 3px;
    padding: 20px 0 60px 0;
    position: relative;
    .list {
      position: relative;
      overflow: auto;
      user-select: none;
      .list_item {
        padding: 0 15px;
        cursor: grab;
        .list_item_content {
          height: 67px;
          display: flex;
          justify-content: space-between;
          align-items: center;
          border-bottom: 1px solid #E5E5E5;
          .cab_name {
            font-weight: bold;
            width: 250px;
            text-overflow: ellipsis;
            overflow: hidden;
            white-space: nowrap;
          }
        }
      }
    }
    .pagination {
      position: absolute;
      bottom: 20px;
      right: 10px;
    }
  }
  .content_right {
    display: grid;
    place-items: center;
    width: 73%;
    border: 1px solid #E5E5E5;
    border-radius: 3px;
    padding: 20px 50px;
    overflow-x: auto;
    img {
      width: 85px;
      height: 70px;
    }
    .active {
      border: 2px solid red;
    }
    .row {
      display: flex;
    }

    .col {
      display: flex;
      user-select: none;
      .owned {
        position: relative;
        text-align: center;
        &:hover {
          .delete-icon {
            display: grid;
            place-items: center;
          }
        }
        .owned-img {
          cursor: grab;
        }
        .name {
          position: absolute;
          left: 0;
          right: 0;
          font-size: 12px;
        }
        .delete-icon {
          width: 20px;
          height: 20px;
          display: none;
          position: absolute;
          right: 5px;
          top: 5px;
          cursor: pointer;
        }
      }
    }
  }
}

</style>

