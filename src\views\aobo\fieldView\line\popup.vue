<template>
  <div class="pop-item" :class="classMap[data.alarmLevel]">
    <div class="name">{{ data.name }}</div>
    <div v-if="temperature === null || temperature === undefined" class="temperature">------</div>
    <div v-else class="temperature">{{ temperature / 100 }}℃</div>
  </div>
</template>

<script>
export default {
  name: 'Popup',
  props: {
    data: Object,
  },
  data() {
    return {
      classMap: {
        0: 'blue',
        1: 'orange',
        2: 'yellow',
        3: 'red',
      }
    }
  },
  computed: {
    temperature() {
      return this.data.temperature
    },
  },
  methods: {},
}
</script>
<style scoped lang="scss">
.pop-item {
  border-radius: 6px;
  padding: 10px;
  color: white;
  text-align: center;
  // 内阴影
  box-shadow: 0 0 20px 10px #193f84 inset;
  .temperature {
    color: #00e667;
    text-align: center;
    font-weight: bold;
  }

  &.blue {
    box-shadow: 0 0 20px 10px rgba(0, 0, 255, 0.5) inset;
    .temperature {
      color: #0000FF;
    }
  }
  &.orange {
    box-shadow: 0 0 20px 10px rgba(255, 165, 0, 0.5) inset;
    .temperature {
      color: #FFA500;
    }
  }

  &.yellow {
    box-shadow: 0 0 20px 10px rgba(255, 255, 0, 0.5) inset;
    .temperature {
      color: #FFFF00;
    }
  }

  &.red {
    box-shadow: 0 0 20px 10px rgba(255, 0, 0, 0.5) inset;
    .temperature {
      color: red;
    }
  }
}
</style>
