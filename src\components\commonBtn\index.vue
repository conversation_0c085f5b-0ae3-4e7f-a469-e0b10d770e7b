<template>
  <div :class="btnArr[0].type === 'top' ? 'topButton' : 'listButton'">
    <div
      v-for="(item, index) in btnArr"
      :key="item.tagName"
      :class="btnArr[0].type === 'list' ? 'row-btn' : ''"
    >
      <component
        :is="item.type === 'top' ? 'topButton' : 'listButton'"
        :disabled="
          !operationList.includes(item.tagName) ||
            disabledBtn.includes(item.tagName)
        "
        :color="item.color"
        :text="item.name"
        :row="row"
        :tag-name="item.tagName"
        :type-name="item.typeName"
        v-on="$listeners"
      />
      <div
        v-if="btnArr.length !== index + 1 && item.type === 'list'"
        class="line"
      />
    </div>
  </div>
</template>

<script>
import topButton from './modules/topButton'
import listButton from './modules/listButton'

export default {
  name: 'CommonBtn',
  components: {
    topButton,
    listButton
  },
  props: {
    btnArr: { // 按钮数组
      type: Array,
      default: () => []
    },
    operationList: { // 权限数组
      type: Array,
      default: () => []
    },
    disabledBtn: { // 禁用按钮数组
      type: Array,
      default: () => []
    },
    row: { // 选中行
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {}
  },
  created() {},
  methods: {
  }
}
</script>

<style lang="scss" scoped>
.topButton {
  width: 100%;
  display: flex;
  align-items: center;
  // justify-content: space-between;
  justify-content: flex-end;
  > div {
    margin-left: 10px;
  }
}
.listButton {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: space-around;
  .row-btn {
    display: flex;
    align-items: center;
    height: 23px;
  }
  .custom-popper {
    z-index: 9999 !important;
  }
}
.line {
  width: 2px;
  height: 14px;
  font-size: 14px;
  font-family: PingFang SC;
  font-weight: 500;
  background-color: #d5dfeb;
  margin: 0 15px;
}
</style>
