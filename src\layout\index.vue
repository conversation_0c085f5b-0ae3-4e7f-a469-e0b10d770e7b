<template>
  <div
    :class="classObj"
    class="app-wrapper"
  >
    <div class="headerTitle">
      <div class="logoBox">
        <img :src="$store.state.settings.logo">
        <span>{{ $store.state.settings.webTitle }}</span>
      </div>
      <div>
        <div
          class="message"
          :title="msgNum < 100 ? msgNum+'条消息通知' : '99+条通知'"
          @click="dialogFormVisible=true,getMsgList()"
        >
          <span class="msgNum">{{ msgNum > 99 ? '99+' : msgNum }}</span>
          <i
            class="el-icon-bell"
            style="color:#fff"
          />
        </div>
        <div
          class="right-menu"
        >
          <el-dropdown
            class="avatar-container"
            trigger="click"
          >
            <div class="avatar-wrapper">
              <div class="imgBox">
                <img
                  :src="userInfo.avatar || defaultAvatar"
                  class="user-avatar"
                >
              </div>

              <div class="nameTitleBox">
                <span class="nameTitle">{{ userInfo.nickName || '--' }}</span>
                <!-- <i class="el-icon-arrow-down" /> -->
                <img
                  src="@/assets/layout/<EMAIL>"
                  style="width:13px;height:8px;margin-left:5px;"
                >
              </div>

              <el-dropdown-menu
                slot="dropdown"
                class="user-dropdown"
              >
                <el-dropdown-item
                  @click.native="modifyPassword"
                >
                  <span style="display:block;">修改密码</span>
                </el-dropdown-item>
                <el-dropdown-item
                  @click.native="logout"
                >
                  <span style="display:block;">退出登录</span>
                </el-dropdown-item>
              </el-dropdown-menu>
            </div>
          </el-dropdown>
        </div>
      </div>
    </div>
    <div
      v-if="device==='mobile'&&sidebar.opened"
      class="drawer-bg"
      @click="handleClickOutside"
    />
    <sidebar
      class="sidebar-container"
    />
    <div class="main-container">
      <div :class="{'fixed-header':fixedHeader}">
        <!-- <navbar /> -->
      </div>
      <app-main />
    </div>
    <!-- 消息弹框 -->
    <div class="topDialogMsg">
      <el-dialog
        title="消息通知"
        :visible.sync="dialogFormVisible"
        :modal="false"
        :close-on-click-modal="true"
        width="350px"
      >
        <div
          v-if="tableData.length>0"
        >
          <div class="scroll-container">
            <div
              v-for="(item,index) in tableData.slice(0,9)"
              :key="index+'a'"
              style=""
              class="message_item"
              @click="goMsgDEtail(item)"
            >
              <div style="text-align:right;color:#797979;margin-bottom:10px;">{{ item.createTime }}</div>
              <div class="message_content">
                <div class="cirFF5722" />
                <el-tooltip
                  effect="dark"
                  :content="item.msgContent"
                  placement="top"
                >
                  <div
                    class="message_text"
                  >{{ item.msgContent }}</div>
                </el-tooltip>
              </div>
            </div>
          </div>

          <div style="text-align:center;margin-top:20px;">
            <el-button
              type="primary"
              @click="goMsg()"
            >查看更多>></el-button>
          </div>
        </div>
        <div
          v-else
          style="color:#0061CE;font-size:26px;height:160px;line-height:140px;text-align:center"
        >
          暂无未读信息
        </div>
      </el-dialog></div>

    <el-dialog
      title="修改密码"
      :modal-append-to-body="false"
      :visible.sync="dialogVisiblePwd"
      width="35%"
      @closed="close"
    >
      <div class="form-content">
        <el-form
          ref="form"
          :model="form"
          label-width="136px"
        >
          <!-- pattern: /^(?![a-zA-z]+$)(?!\d+$)(?![!@#$%^&\\.*]+$)[a-zA-Z\d!@#$%^&\\.*]{6,16}$/, -->

          <el-form-item
            label="请输入原始密码"
            :rules="[
              {
                required: true,
                message: '请输入原始密码',
                trigger: 'blur',
              },
            ]"
            prop="oldPassword"
          >
            <el-input
              v-model="form.oldPassword"
              placeholder="请输入"
              class="number-limit"
            />
          </el-form-item>
          <el-form-item
            label="请输入新密码"
            :rules="[
              {
                required: true,
                message: '请输入新密码',
                trigger: 'blur',
              },
              {
                required: true,
                message: '请输入长度为 6 - 16 的字母或数字或特殊字符',
                pattern: /^(?:\d+|[a-zA-Z]+|[!@#$%^&\\.*]+){6,16}$/,
                trigger: 'blur',
              },
            ]"
            prop="newPassword"
          >
            <el-input
              v-model="form.newPassword"
              placeholder="请输入"
              class="number-limit"
            />
          </el-form-item>
          <el-form-item
            label="请再次确认新密码"
            :rules="[
              {
                required: true,
                message: '请输入新密码',
                trigger: 'blur',
              },
              {
                required: true,
                message: '请输入长度为 6 - 16 的字母或数字或特殊字符',
                pattern: /^(?:\d+|[a-zA-Z]+|[!@#$%^&\\.*]+){6,16}$/,
                trigger: 'blur',
              },
            ]"
            prop="newConfigPassword"
          >
            <el-input
              v-model="form.newConfigPassword"
              placeholder="请输入"
              class="number-limit"
            />
          </el-form-item>
        </el-form>
      </div>
      <span
        slot="footer"
        class="dialog-footer"
      >
        <el-button @click="close">取 消</el-button>
        <el-button
          :loading="pwdLoding"
          type="primary"
          @click="sunmitPassword"
        >确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
// eslint-disable-next-line import/no-cycle
// import { getFile } from '@/api/personal'
// eslint-disable-next-line import/no-cycle
// import { getNoMsgNum, getNoMsgNumList, getYesMsg } from '@/api/messageCenter'
// import { verifyPasswordM } from '@/utils/toolsValidate'
import { updatePassword } from '@/api/user'
import { page, checkMsg } from '@/api/messageCenter'
import Cookies from 'js-cookie'
import { useDebounceFn } from '@/utils'
import {
  // Navbar,
  Sidebar, AppMain
} from './components'
// eslint-disable-next-line import/no-cycle
import ResizeMixin from './mixin/ResizeHandler'

export default {
  name: 'Layout',
  components: {
    // Navbar,
    Sidebar,
    AppMain
  },
  mixins: [ResizeMixin],
  data() {
    return {
      // logo: require('@/assets/logo.png'),
      // userInfo: {},
      menuList: JSON.parse(localStorage.getItem('menuList')),
      avatar: '',
      defaultAvatar: require('@/assets/layout/<EMAIL>'),
      msgNum: 0,
      dialogFormVisible: false,
      tableData: [],
      clearInterval: '',
      actualName: '',
      dialogVisiblePwd: false,
      form: {},
      pwdLoding: false,
      interval: null,
      socket: null, // WebSocket
      socketCode: 1 // 状态码
    }
  },
  computed: {
    ...mapGetters([
      'sidebar', 'userInfo'
    ]),
    sidebar() {
      return this.$store.state.app.sidebar
    },
    device() {
      return this.$store.state.app.device
    },
    fixedHeader() {
      return this.$store.state.settings.fixedHeader
    },
    classObj() {
      return {
        hideSidebar: !this.sidebar.opened,
        openSidebar: this.sidebar.opened,
        withoutAnimation: this.sidebar.withoutAnimation,
        mobile: this.device === 'mobile'
      }
    }
  },
  mounted() {
    // this.userInfo = JSON.parse(localStorage.getItem('userInfo'))
    // this.userInfo = this.$store.state.user.userInfo
    // if (this.userInfo.avatar) {
    //   // 获取头像
    //   getFile(this.userInfo.avatar).then((res) => {
    //     this.avatar = window.URL.createObjectURL(res.data)
    //   })
    // }
    // this.getNoMsgNumFun()
    this.getMsgList()
    // this.interval = setInterval(() => {
    //   this.getMsgList()
    // }, 10000)
    this.websocketConnect()
  },
  beforeDestroy() {
    this.socket.close()
    this.socket = null
    // 清除定时器
    clearInterval(this.clearInterval)
    // clearInterval(this.interval)
  },
  methods: {
    handleClickOutside() {
      this.$store.dispatch('app/closeSideBar', { withoutAnimation: false })
    },
    // 修改密码
    modifyPassword() {
      this.dialogVisiblePwd = true
      // const authorities = localStorage.getItem('menuList')
      // if (!authorities.includes('GRZX-MMXG')) {
      //   this.$message.warning('暂无权限')
      //   return
      // }
      // this.$router.push('/personal/reset')
    },
    async logout() {
      await this.$store.dispatch('user/logout')
      this.$router.push(`/login?redirect=${this.$route.fullPath}`)
    },
    // 获取未读消息数量
    // getNoMsgNumFun() {
    //   getNoMsgNum().then((res) => {
    //     if (res.data) {
    //       this.msgNum = res.data
    //     }
    //   })
    //   // eslint-disable-next-line no-underscore-dangle
    //   const _this = this
    //   this.clearInterval = setInterval(() => {
    //     getNoMsgNum().then((res) => {
    //       if (res.data) {
    //         _this.msgNum = res.data
    //       }
    //     })
    //   }, 10000)
    // },
    // 获取未读消息列表
    // getMsgList() {
    //   getNoMsgNumList().then((res) => {
    //     // this.tableData = res.data
    //     if (res.data.length <= 10) {
    //       this.tableData = res.data
    //     } else {
    //       this.tableData = res.data.slice(0, 9)
    //     }
    //   })
    // },
    websocketConnect() {
      // 石伟
      this.socket = new WebSocket('ws://192.168.0.62:21021/ws')
      // 测试服
      // this.socket = new WebSocket('wss://aobo-dts-test.vankeytech.com:9902/ws')
      // 线上
      // this.socket = new WebSocket('ws://10.108.183.106/ws')
      // 监听socket连接
      this.socket.onopen = () => {
        const data = {
          code: this.socketCode,
          token: Cookies.get('governance_token')
        }
        this.socket.send(JSON.stringify(data))
      }
      // 监听socket错误信息
      this.socket.onerror = (err) => {
        console.log(err, 'err')
      }
      // 消息防抖处理
      const debouncedFn = useDebounceFn((msg) => {
        const res = JSON.parse(msg.data)
        if (res.code === 200 && this.socketCode === 1) {
          this.socketCode = 3
          const data = {
            code: this.socketCode,
            msgId: res.message
          }
          this.socket.send(JSON.stringify(data))
        } else if (res.code === 200 && res.message) {
          this.getMsgList()
        }
      }, 1000)
      // 监听socket消息
      this.socket.onmessage = debouncedFn
      this.socket.onclose = () => {
        if (!Cookies.get('governance_token')) return
        this.socketCode = 1
        this.socket = null
        setTimeout(() => {
          this.websocketConnect()
        }, 10000)
      }
    },
    getMsgList() {
      const data = {
        pageNum: 1,
        pageSize: 100,
        query: {
        }
      }
      page(data).then((res) => {
        this.tableData = res.data.records
        this.total = parseInt(res.data.total, 0)
        this.msgNum = this.tableData.length
      }).catch((res) => {
      })
    },
    // 去消息中心
    goMsg() {
      if (!this.menuList.includes('XTGL-XXZX')) {
        this.$message.warning('暂未配置消息中心权限，请联系管理员！')
        return
      }
      this.dialogFormVisible = false
      this.$router.push({ path: '/systemManage/messageCenter' })
    },
    goMsgDEtail(row) {
      if (!this.menuList.includes('BJXX')) {
        this.$message.warning('暂未配置报警信息权限，请联系管理员！')
        return
      }
      this.$router.push({ path: '/alarmInfo/alarmInfo' })
      this.dialogFormVisible = false
      checkMsg({
        id: row.id
      }).then((res) => {
        console.log(res)
      })
    },

    close() {
      this.form = {
        password: ''
      }
      this.$refs.form.resetFields()
      this.dialogVisiblePwd = false
    },
    async sunmitPassword() {
    // console.log(verifyPasswordM(this.form.password))
      await this.$refs.form.validate(async(valid) => {
        if (valid) {
          // console.log(verifyPasswordM(this.form.password))
          if (this.form.newConfigPassword !== this.form.newPassword) {
            this.$message.warning('两次输入密码不一致！')
            return
          }
          const info = window.localStorage.getItem('userInfo')
          const { id } = JSON.parse(info)
          const data = {
            id,
            oldPassword: this.form.oldPassword,
            newPassword: this.form.newPassword,
            newConfigPassword: this.form.newConfigPassword
            // password: this.form.password
          }
          this.pwdLoding = true
          await updatePassword(data).then((res) => {
          // this.$message.success('修改密码成功')
            this.dialogVisible = false
            this.$alert('修改密码成功，将退出重新登录', '提示', {
              confirmButtonText: '确定',
              callback: async(action) => {
                // localStorage.clear()
                // this.$router.push(`/login`)
                // localStorage.removeItem('userinfos')
                // store.dispatch('user/logout')
                // store.dispatch('user/resetToken').then(() => {
                //   window.location.reload()
                // })
                await this.$store.dispatch('user/logout')
                this.$router.push(`/login?redirect=${this.$route.fullPath}`)
              }
            })
          }).finally(() => {
            this.pwdLoding = false
          })
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
  @import "~@/styles/mixin.scss";
  @import "~@/styles/variables.scss";
  #app{
    margin-bottom: 60px;
    background: #F5F5F3;
  }
  .headerTitle{
    background: url('../assets/login/bg_title.png');
    background-size: 100% 100%;
    display: flex;
    // justify-content: flex-end;
    justify-content: space-between;
    align-items: center;
    box-shadow: 0px 0px 11px 1px rgba(52, 61, 102, 0.1);
    height: 70px;
    // width: calc(100% - 256px);
    width: calc(100%);
    position: fixed;
    top: 0;
    right: 0;
    z-index: 2;
    .logoBox{
      margin-left: 30px;
      display: flex;
      align-items: center;
      img{
        height: 40px;
        width: 42px;
      }
      span{
        white-space: nowrap;
        margin-left: 12px;
        font-size: 24px;
        font-family: PingFang SC, PingFang SC;
        font-weight: 600;
        color: #fff;
        line-height: 0px;
      }
    }
    .logo-words{
      font-size: 20px;
      color: #A09B8F;
    }
    >div{
      display: flex;
      justify-content: flex-end;
      align-items: center;
    }
    .message{
      cursor: pointer;
      display: inline-block;
      margin-right: 30px;
      position: relative;
      .msgNum{
        display: inline-block;
        // width: 16px;
        // height: 16px;
        padding: 2px 5px;
        background-color:#FF5722 ;
        color: #FFFFFF;
        border-radius: 2px;
        text-align: center;
        // line-height: 16px;
        font-size: 12px;
        position: absolute;
        top: -5px;
        left: -6px;
      }
      .el-icon-bell{
        font-size: 22px;
      }
    }
  }
  .app-wrapper {
    @include clearfix;
    position: relative;
    height: calc(100%);
    width: 100%;
    // padding-top: 90px;
    box-sizing: border-box;
    &.mobile.openSidebar{
      position: fixed;
      top: 0;
    }
  }
  .drawer-bg {
    background: #000;
    opacity: 0.3;
    width: 100%;
    top: 0;
    height: 100%;
    position: absolute;
    z-index: 999;
  }

  .fixed-header {
    position: fixed;
    top: 0;
    right: 0;
    z-index: 9;
    width: calc(100% - #{$sideBarWidth});
    transition: width 0.28s;
  }

  .hideSidebar .fixed-header {
    width: calc(100% - 90px)
  }

  .mobile .fixed-header {
    width: 100%;
  }
  .logo{
    display: flex;
    align-items: center;
    .sidebar-logo{
      width: 277px;
    }
  }

  .right-menu {
  height: 60px;
  &:focus {
    outline: none;
  }
  .avatar-container {
    margin-right: 30px;
    .avatar-wrapper {
      height: 60px;
      position: relative;
      display: flex;
      align-items: center;
      .imgBox{
        //margin-top: 10px;
        .user-avatar {
          cursor: pointer;
          width: 40px;
          height: 40px;
          border-radius: 50%;
          margin-right: 15px;
        }
      }
      .nameTitleBox{
        height: 60px;
        line-height: 60px;
        i{
          cursor: pointer;
        }
        .nameTitle{
          color: #fff;
        }
        .el-icon-caret-bottom {
          cursor: pointer;
          font-size: 14px;
        }
      }
    }
  }
  }
 .el-popper[x-placement^=bottom]{
    margin-top: 0px !important;
  }
 .cirFF5722{
    display:inline-block;
    width:10px;
    height:10px;
    background:#FF5722;
    border-radius: 5px;
    margin-right: 6px;
  }
  // 消息弹框
  ::v-deep .topDialogMsg{
    .el-dialog{
       display: flex;
       flex-direction: column;
       margin:0 !important;
       transform:translate(-50%,-50%);
       /*height:600px;*/
       max-height:calc(100% - 10px);
       max-width:calc(100% - 30px);
       position:absolute;
       top:50%;
       left:90%;
       }
   }
   ::v-deep .el-dialog__body {
       padding: 20px !important;
   }
.message_item {
  font-size:15px;
  border-bottom:1px solid #ebeef5;
  padding:16px 0;
  box-sizing:content-box;
  cursor: pointer;
  .message_content {
    display: flex;
    align-items: center;
    .message_text {
      line-height:24px;
      width: 300px;
      text-overflow: ellipsis;
      overflow: hidden;
      white-space: nowrap;
      font-weight: bold;
    }
  }
}

.scroll-container {
  min-height: 100px;
  max-height: 60vh;
  overflow-y: auto;
  // 隐藏滚动条
  scrollbar-width: none;
}
</style>
