<template>
  <div>
    <!-- :popper-append-to-body="false" -->
    <!-- :value="bindValue" -->
    <el-select
      popper-class="virtualselect"
      class="virtual-select-custom-style"
      :value="defaultValue"
      filterable
      :filter-method="filterMethod"
      default-first-option
      clearable
      :placeholder="placeholder"
      :disabled="disabled"
      :multiple="isMultiple"
      :allow-create="allowCreate"
      v-bind="$attrs"
      @visible-change="visibleChange"
      v-on="$listeners"
      @clear="clearChange"
      @focus="focusChange"
    >
      <virtual-list
        ref="virtualList"
        class="virtualselect-list"
        :data-key="value"
        :data-sources="selectArr"
        :data-component="itemComponent"
        :keeps="keepsParams"
        :extra-props="{
          label: label,
          value: value,
          isRight: isRight,
          isConcat: isConcat,
          concatSymbol: concatSymbol,
          selectOptionWidth: selectOptionWidth
        }"
      />
    </el-select>
  </div>
</template>
<script>
import {
  validatenull
} from '@/utils'
import virtualList from 'vue-virtual-scroll-list'
import ElOptionNode from './ElOptionNode.vue'

export default {
  name: 'VirtualSelect',
  components: {
    'virtual-list': virtualList
  },
  model: {
    // prop: 'bindValue',
    prop: 'defaultValue',
    event: 'change'
  },
  props: {
    // // 父组件传的值
    // selectData: {
    //   type: Object,
    //   default() {
    //     return {}
    //   }
    // },
    // 数组
    list: {
      type: Array,
      default() {
        return []
      }
    },
    // 显示名称
    label: {
      type: String,
      default: ''
    },
    // 标识
    value: {
      type: String,
      default: ''
    },
    // 是否拼接label | value
    isConcat: {
      type: Boolean,
      default: false
    },
    // 拼接label、value符号
    concatSymbol: {
      type: String,
      default: ' | '
    },
    // 显示右边
    isRight: {
      type: Boolean,
      default: false
    },
    // 加载条数
    keepsParams: {
      type: Number,
      default: 10
    },
    // 绑定值 v-model
    // bindValue: {
    defaultValue: {
      type: [String, Array],
      default() {
        // if (typeof this.bindValue === 'string') return ''
        if (typeof this.defaultValue === 'string') return ''
        return []
      }
    },
    // 是否多选
    isMultiple: {
      type: Boolean,
      default: false
    },
    // 输入框占位文本
    placeholder: {
      type: String,
      default: '请选择'
    },
    // 是否禁用
    disabled: {
      type: Boolean,
      default: false
    },
    // 是否允许创建条目
    allowCreate: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      itemComponent: ElOptionNode,
      selectArr: [],
      // defaultValue: null, // 绑定的默认值
      selectOptionWidth: null, // 下拉框宽度
      dataList: []
    }
  },
  watch: {
    list() {
      this.init()
    },
    // bindValue: {
    defaultValue: {
      handler(val, oldVal) {
        // this.defaultValue = this.bindValue
        if (validatenull(val)) this.clearChange()
        this.init()
      },
      immediate: false,
      deep: true
    }
  },
  mounted() {
    // this.defaultValue = this.bindValue
    this.init()
  },
  methods: {
    init() {
      this.list.forEach((item) => {
        if (validatenull(item[this.label])) {
          item[this.label] = item[this.value]
        }
      })
      this.dataList = this.list
      if (!this.defaultValue || this.defaultValue?.length === 0) {
        // this.selectArr = this.list
        this.selectArr = this.dataList
      } else {
        // 回显问题
        // 由于只渲染固定keepsParams（10）条数据,当默认数据处于10条之外,在回显的时候会显示异常
        // 解决方法:遍历所有数据,将对应回显的那一条数据放在第一条即可
        this.selectArr = JSON.parse(JSON.stringify(this.dataList))
        // this.selectArr = JSON.parse(JSON.stringify(this.list))
        let obj = {}
        if (typeof this.defaultValue === 'string' && !this.isMultiple) {
          if (this.allowCreate) {
            const arr = this.selectArr.filter((val) => val[this.value] === this.defaultValue)
            if (arr.length === 0) {
              const item = {}
              // item[this.value] = `Create-${this.defaultValue}`
              item[this.value] = this.defaultValue
              item[this.label] = this.defaultValue
              item.allowCreate = true
              this.selectArr.push(item)
              this.$emit('selChange', item)
            } else {
              this.$emit('selChange', arr[0])
            }
          }

          // 单选
          for (let i = 0; i < this.selectArr.length; i++) {
            const element = this.selectArr[i]
            if (element[this.value]?.toLowerCase() === this.defaultValue?.toLowerCase()) {
              obj = element
              this.selectArr?.splice(i, 1)
              break
            }
          }
          this.selectArr?.unshift(obj)
        } else if (this.isMultiple) {
          if (this.allowCreate) {
            this.defaultValue.map((v) => {
              const arr = this.selectArr.filter((val) => val[this.value] === v)
              if (arr?.length === 0) {
                const item = {}
                // item[this.value] = `Create-${v}`
                item[this.value] = v
                item[this.label] = v
                item.allowCreate = true
                this.selectArr.push(item)
                this.$emit('selChange', item)
              } else {
                this.$emit('selChange', arr[0])
              }
            })
          }

          // 多选
          for (let i = 0; i < this.selectArr.length; i++) {
            const element = this.selectArr[i]
            this.defaultValue?.map((val) => {
              if (element[this.value]?.toLowerCase() === val?.toLowerCase()) {
                obj = element
                this.selectArr?.splice(i, 1)
                this.selectArr?.unshift(obj)
              }
            })
          }
        }
      }
    },
    // 搜索
    filterMethod(query) {
      if (!validatenull(query?.trim())) {
        this.$refs.virtualList.scrollToIndex(0) // 滚动到顶部
        setTimeout(() => {
          // const orgArr = this.list.filter(item => {
          const orgArr = this.dataList.filter((item) => (this.isRight || this.isConcat
            ? (item[this.label].trim()?.toLowerCase()?.indexOf(query?.trim()?.toLowerCase()) > -1 || item[this.value]?.toLowerCase()?.indexOf(query?.trim()?.toLowerCase()) > -1)
            : item[this.label]?.toLowerCase()?.indexOf(query?.trim()?.toLowerCase()) > -1))
          this.selectArr = this.handleSort(orgArr) || []
        }, 100)
      } else {
        setTimeout(() => {
          this.init()
        }, 100)
      }
    },
    // 按拼音字母数字顺序排序
    handleSort(arr) {
      const sortArr = arr.sort((a, b) => {
        const reg = /[a-zA-Z0-9]/
        const x = a[this.label]
        const y = b[this.label]
        if (reg.test(x) || reg.test(y)) {
          if (x > y) {
            return 1
          } if (x < y) {
            return -1
          }
          return 0
        }
        return x.localeCompare(y)
      })
      return sortArr
    },
    visibleChange(bool) {
      if (!bool) {
        this.$refs.virtualList.reset()
        this.init()
      }
      this.$emit('visible-change', bool)
    },
    clearChange() {
      // if (typeof this.defaultValue === 'string') {
      //   this.defaultValue = ''
      // } else if (this.isMultiple) {
      //   this.defaultValue = []
      // }
      this.visibleChange(false)
    },
    focusChange(event) {
      this.setOptionWidth(event)
    },
    setOptionWidth(event) {
      // 下拉框弹出时，设置弹框的宽度
      this.$nextTick(() => {
        this.selectOptionWidth = `${event.srcElement.offsetWidth}px`
      })
    }
  }
}
</script>
<style lang="scss" scoped>
// .virtualselect ::v-deep .el-select-dropdown__item {
// // .virtual-select-custom-style ::v-deep .el-select-dropdown__item {
//   // 设置最大宽度，超出省略号，鼠标悬浮显示
//   // options 需写 :title="source[label]"
//   // width: 250px;
//   // display: inline-block;
//   // overflow: hidden;
//   // text-overflow: ellipsis;
//   // white-space: nowrap;
//   // z-index: 9;
// }

.virtualselect {
  // 设置最大高度
  max-height:245px;
  overflow-y:auto;
  // &-list {
  //   max-height:245px;
  //   overflow-y:auto;
  // }
}
.virtualselect-list {
  max-height:245px;
  overflow-y:auto;
}
.el-select {
  width: 100%;
}
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
  background-color: transparent;
  cursor: pointer;
  margin-right: 5px;
}
::-webkit-scrollbar-thumb {
  background-color: rgba(144,147,153,.3) !important;
  border-radius: 3px !important;
}
::-webkit-scrollbar-thumb:hover{
  background-color: rgba(144,147,153,.5) !important;
}
::-webkit-scrollbar-track {
  background-color: transparent !important;
  border-radius: 3px !important;
  -webkit-box-shadow: none !important;
}
::v-deep  .el-select__tags {
  flex-wrap: unset;
  overflow: auto;
}
</style>
<style>
.virtualselect .el-select-dropdown__item{
  display: block;
  /* max-width: 350px; */
  overflow: visible;
}
</style>
