<template>
  <div
    id="app"
    v-loading="infoLoading"
  >
    <router-view />
  </div>
</template>

<script>
import { getWebInfo } from '@/api/user'
import { changeFavicon } from '@/utils'

export default {
  name: 'App',
  components: {
  },
  data() {
    return {
      infoLoading: false
    }
  },
  created() {
    this.infoLoading = true
    getWebInfo().then((res) => {
      const { data } = res
      if (data?.webTitle) {
        this.$store.commit('settings/CHANGE_SETTING', { key: 'webTitle', value: data.webTitle })
      }
      const logo = data?.logo || require('@/assets/login/logo_title.png')
      // 设置网站图标
      changeFavicon(logo)

      this.$store.commit('settings/CHANGE_SETTING', { key: 'logo', value: logo })
      const loginBg = data?.loginBackground || require('@/assets/login/<EMAIL>')
      this.$store.commit('settings/CHANGE_SETTING', { key: 'loginBg', value: loginBg })
      this.$store.commit('settings/CHANGE_SETTING', { key: 'singleLineRetainTime', value: data?.singleLineRetainTime })
      this.$store.commit('settings/CHANGE_SETTING', { key: 'systemInfo', value: data })
    }).finally(() => {
      this.infoLoading = false
    })
  }

}
</script>
<style lang="scss">
.map-main {
  width: 100%;
  height: 55vh;
}
#tip {
  color: #333;
  position: absolute;
  top: 90px;
  right: 300px;
  border: none;
  box-shadow:0px 2px 24px 0px rgba(0,29,64,0.12);
  padding: 0;
  border-radius: 12px;
  input[type='text'] {
    border: none;
    height: 50px;
    border: 0;
    padding: 0 80px 0 30px;
    width: 550px;
    border-radius: 12px;
    outline: none;
    font-size: 16px;
    input::-webkit-input-placeholder {
      color: #999;
    }
  }

  .map-search-icon {
    width: 30px;
    height: 30px;
    position: absolute;
    right: 30px;
    top: 11px;
    cursor: pointer;
  }
}
.amap-sug-result {
  z-index: 9999;
}
</style>
