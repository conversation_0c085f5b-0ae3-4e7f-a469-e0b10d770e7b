<template>
  <div>
    <div
      class="pop_item"
      :class="isAlarm ? 'alarm1' : 'normal1'"
      style="width:100px;height:80px"
    >
      <div class="name">{{ floor }}层</div>
      <div
        v-if="temperature"
        class="temperature"
      >{{ temperature.value / 100 }}℃</div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'PopupHorizontal',
  props: {
    data: Object,
    threshold: Number
  },
  computed: {
    temperature() {
      return this.data.temperature
    },
    isAlarm() {
      return this.data.temperature && this.data.temperature.value > this.threshold
    },
    floor() {
      return this.data.tierStr && this.data.tierStr.match(/\d+/)[0]
    }
  }
}

</script>
<style scoped lang="scss">
.pop_item {
  position: absolute;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  width: 75px;
  height: 80px;
  font-size: 11px;
  // font-weight: bold;
  line-height: 17px;
  z-index: 1;
  cursor: pointer;
  .alarm_water {
    position: absolute;
    bottom: -15px;
    left: 0;
    width: 100%;
    height: 50%;
  }
  .alarm_kuang {
    position: absolute;
    top: -39px;
    left: -13px;
    width: 122px;
    height: 205px;
  }
  .alarm_kuang1 {
    position: absolute;
    top: -48px;
    left: -18px;
    width: 140px;
    height: 258px;
  }
  .pop_kuang {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
  }
  .number {
    color: #C4DDFF;
  }
  .name {
    color: #C4DDFF;
  }
  .temperature {
    font-weight: bold;
    color: #38EA08;
  }
}

.alarm1 {
  background: url(~@/assets/page/pop_left_alarm.png) no-repeat;
  background-size: 100% 100%;
  .temperature {
    color: #FF433D;
  }
}
.normal1 {
  background: url(~@/assets/page/pop_left.png) no-repeat;
  background-size: 100% 100%;
}

</style>
