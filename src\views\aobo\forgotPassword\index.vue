<template>
  <div class="forgotBox">
    <div class="headerTitle">
      <div class="logoBox">
        <img src="@/assets/layout/<EMAIL>">
        <span>中油奥博远程支撑系统后台管理</span>
      <!-- <span>（试运行）</span> -->
      </div>
    </div>
    <div class="content">
      <div class="centen">
        <div class="title">忘记密码</div>
        <el-steps
          :active="stepActive"
          align-center
        >
          <el-step
            title="验证身份"
            description=""
          />
          <el-step
            title="设置新密码"
            description=""
          />
          <el-step
            title="重置成功"
            description=""
          />
        </el-steps>
        <div
          v-if="stepActive==1"
          style="width:570px;margin:auto;margin-top:120px"
        >
          <div
            class="first"
            style="margin:20px 0"
          >
            <div style="color: #ff4444; margin-left:15px">*</div>
            <div style="color: #5a5a5a; margin: 0 20px 0 5px;">手机号码:</div>
            <div class="sysInput">
              <el-input
                v-model="tel"
                placeholder="请输入账号"
                style="width:420px"
                class="input-f"
                :maxlength="11"
              />
            </div>
          </div>
          <div
            class="first"
            style="margin:20px 0"
          >
            <div style="color: #ff4444; margin-left:15px">*</div>
            <div style="color: #5a5a5a; margin: 0 20px 0 5px">手机验证码:</div>
            <div class="sysInput">
              <el-input
                v-model="codeNum"
                placeholder="验证码"
                style="width:288px"
                class="input-f"
                :maxlength="10"
              />
            </div>
            <div
              class="yanzhengma"
              @click="handleGetCode"
            >{{ secondsRemaining===0? '获取验证码': (secondsRemaining+'s') }}</div>
          </div>
          <div
            class="btn"
            @click="nextOne"
          >下一步</div>
        </div>
        <div
          v-if="stepActive==2"
          style="width:570px;margin:auto;margin-top:120px"
        >
          <div
            class="first"
            style="margin:20px 0"
          >
            <div style="color: #ff4444; margin-left:15px">*</div>
            <div style="margin: 0 20px 0 5px">输入密码：</div>
            <div class="sysInput">
              <el-input
                v-model="passwordOne"
                placeholder="6 - 16位密码"
                style="width:420px"
                class="input-f"
                :maxlength="10"
              />
            </div>
          </div>
          <div
            class="first"
            style="margin:20px 0"
          >
            <div style="color: #ff4444; margin-left:15px">*</div>
            <div style="color: #5a5a5a; margin: 0 20px 0 5px">确认密码：</div>
            <div class="sysInput">
              <el-input
                v-model="passwordTwo"
                placeholder="确认密码"
                style="width:420px"
                class="input-f"
                :maxlength="10"
              />
            </div>
          </div>
          <div
            class="btn"
            @click="nextTwo"
          >下一步</div>
        </div>
        <div
          v-if="stepActive==3"
          style="margin:auto;margin-top:120px"
        >
          <div class="successCg">
            <div class="iconCg"><img src="../../../assets/login/<EMAIL>"></div>
            <div>
              恭喜您，重置密码成功！
            </div>
          </div>
          <div
            class="btn"
            @click="goLogin"
          >返回登录</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'

import { testPassword } from '@/utils/toolsValidate'
import { getTelCode, checkCode, restPassWord } from '@/api/user'
import store from '@/store'
import router, { resetRouter } from '@/router/index'

export default {
  name: 'PlateData',
  components: {
  },
  data() {
    return {
      btnArrTop: [
        {
          type: 'top',
          name: '导出',
          tagName: 'export',
          typeName: 'success'
        }
      ],
      btnArrList: [
        {
          type: 'list',
          name: '重命名',
          tagName: 'reName',
          color: '#1071E2'
        },
        {
          type: 'list',
          name: '下载',
          tagName: 'download',
          color: '#1071E2'
        },
        {
          type: 'list',
          name: '权限管理',
          tagName: 'authorityManage',
          color: '#1071E2'
        },
        {
          type: 'list',
          name: '删除',
          tagName: 'del',
          color: '#1071E2'
        }
      ],
      operationList: [],
      formSearch: {
        keywords: '',
        loginType: '',
        loginPort: '',
        startTime: '',
        endTime: '',
        roleIds: [],
        date: []
      },
      showAdvancedSearch: true,
      // 0:登出1:登录  类型
      typeList: [{
        value: 0,
        label: '登出'
      }, {
        value: 1,
        label: '登录'
      }],
      roleData: [], // 角色下拉列表
      portData: [], // 端口下拉列表

      selectList: [], // 选中列表
      loading: false,
      tableData: [],
      pageNum: 1,
      pageSize: 10,
      total: 0,
      selectRow: {},

      dialogVisible: false,
      dialogVisible1: false,
      isAdd: true,
      detailData: {},
      activeName: 'first',
      enterpriseProjectsList: [],
      stepActive: 1,
      tel: '',
      codeNum: '',
      passwordOne: '',
      passwordTwo: '',
      secondsRemaining: 0
    }
  },
  computed: {
    ...mapGetters([
      'tableHeaderStyle', 'btnAuthority'
    ])
  },
  // 监听路由获取当前页面按钮权限
  watch: {
    $route: {
      handler(val, oldVal) {
        const btnAuthority = [...this.btnAuthority]
        const code = val.meta.code || ''
        const objArray = btnAuthority.filter((x) => x.code === code) || []
        const obj = objArray ? objArray[0] : {}
        this.operationList = obj.functionPermissionCode || []
        console.log(code, obj)
      },
      // 深度观察监听
      deep: true,
      immediate: true
    }
  },
  mounted() {
    this.getroleDropdown()
    this.getportDropDownBox()
    this.getList(1)
    // this.operationList.push('export')
  },
  methods: {
    // 获取验证码
    handleGetCode() {
      getTelCode(this.tel).then((res) => {
        this.secondsRemaining = 60 // 初始化剩余秒数为60
        setTimeout(this.countdown, 1000) // 开始倒计时
      })
    },
    // 倒计时
    countdown() {
      if (this.secondsRemaining > 0) {
        // countdown.innerHTML = `距结束还有${this.secondsRemaining}秒`
        this.secondsRemaining--
        setTimeout(this.countdown, 1000) // 每隔1秒更新一次倒计时
      } else {
        //  this.secondsRemaining
      }
    },
    //     / JavaScript部分
    // var countdown = document.getElementById("countdown"); // 获取显示倒计时的元素
    // var secondsRemaining = 60; // 初始化剩余秒数为60

    nextOne() {
      if (!this.tel) {
        this.$message.warning('请输入手机号码')
        return
      }
      if (!this.codeNum) {
        this.$message.warning('请输入手机验证码')
        return
      }
      const data = {
        tel: this.tel,
        code: this.codeNum
      }
      checkCode(data).then((res) => {
        this.stepActive = 2
      })
    },
    nextTwo() {
      console.log(testPassword(this.passwordOne))
      if (testPassword(this.passwordOne) === false) {
        this.$message.warning('请输入长度为 6 - 16 的字母或数字或特殊字符')
        return
      }
      if (this.passwordOne !== this.passwordTwo) {
        this.$message.warning('两次输入密码不一致')
        return
      }
      const data = {
        tel: this.tel,
        newPassword: this.passwordOne,
        newConfigPassword: this.passwordTwo
      }
      restPassWord(data).then(() => {
        this.stepActive = 3
      })
    },
    // 返回登录
    goLogin() {
      // this.$router.push('/')
      store.dispatch('user/resetToken').then(() => {
        store.commit('permission/HAS_FILTER_ROUTES', false)
        resetRouter()
        router.push(`/login`)
      })
    }

  }

}
</script>

<style lang="scss" scoped>
.forgotBox{
  width: 100vw;
  height: 100vh;
}
.headerTitle{
    display: flex;
    // justify-content: flex-end;
    justify-content: space-between;
    align-items: center;
    background: #FFFFFF;
    box-shadow: 0px 0px 11px 1px rgba(52, 61, 102, 0.1);
    height: 70px;
    // width: calc(100% - 256px);
    width: calc(100%);
    position: fixed;
    top: 0;
    right: 0;
    z-index: 2;
    .logoBox{
      margin-left: 30px;
      display: flex;
      align-items: center;
      img{
        height: 40px;
        width: 42px;
      }
      span{
        white-space: nowrap;
        margin-left: 12px;
        font-size: 24px;
        font-family: PingFang SC, PingFang SC;
        font-weight: 600;
        color: #101010;
        line-height: 0px;
      }
    }
    .logo-words{
      font-size: 20px;
      color: #A09B8F;
    }
    >div{
      display: flex;
      justify-content: flex-end;
      align-items: center;
    }
    .message{
      cursor: pointer;
      display: inline-block;
      margin-right: 30px;
      position: relative;
      .msgNum{
        display: inline-block;
        // width: 16px;
        // height: 16px;
        padding: 2px 5px;
        background-color:#FF5722 ;
        color: #FFFFFF;
        border-radius: 2px;
        text-align: center;
        // line-height: 16px;
        font-size: 12px;
        position: absolute;
        top: -5px;
        left: -6px;
      }
      .el-icon-bell{
        font-size: 22px;
      }
    }
  }
  .content{
    height: calc(100vh);
    background: #F4F7FE;
    padding-top: 100px;
    box-sizing: border-box;
    .centen{
      width: 1200px;
      // height: 930px;
      height: calc(100% - 70px);
      background: #fff;
      border-radius: 20px 20px 20px 20px;
      padding: 30px;
      box-sizing: border-box;
      opacity: 1;
      margin:auto;
      .title{
        font-size: 21px;
        font-family: PingFang SC, PingFang SC;
        font-weight: bold;
        color: #40424E;
        margin-bottom: 60px;
      }
    }
  }
.first {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  margin:auto;
  font-size: 18px;
  font-family: PingFang SC, PingFang SC;
  font-weight: 500;
  color: #40424E;
}
.yanzhengma{
  width: 120px;
  height: 50px;
  line-height: 50px;
  background: url('~@/assets/login/yzm.png') no-repeat;
  background-size: 100% 100%;
  font-size: 17px;
  font-family: PingFang SC, PingFang SC;
  font-weight: 400;
  color: #1071E2;
  text-align: center;
  margin-left: 12px;
}
.btn{
  width: 380px;
  height: 60px;
  background: #1071E2;
  box-shadow: 0px 5px 10px 1px rgba(11,136,251,0.25);
  border-radius: 2px 2px 2px 2px;
  opacity: 1;
  text-align: center;
  line-height: 60px;
  font-size: 18px;
  font-family: PingFang SC, PingFang SC;
  font-weight: 500;
  color: #FFFFFF;
  text-align: center;
  margin: auto;
  margin-top: 180px;
}
.input-f{
  // background: url('~@/assets/login/<EMAIL>') no-repeat;
  // background-size: 100% 100%;

  ::v-deep .el-input__inner{
      height: 50px;
      line-height: 50px;
      background: url('~@/assets/login/inputBg.png') no-repeat;
      background-size: 100% 100%;
      font-size: 17px;
      font-family: PingFang SC, PingFang SC;
      font-weight: 400;
      color: #959598;
  }
}

.successCg{
  // margin: auto;
  text-align: center;
  font-size: 20px;
  font-family: PingFang SC, PingFang SC;
  font-weight: bold;
  color: #40424E;
  .iconCg{
    width: 52px;
    height: 52px;
    margin: auto;
    margin-bottom: 36px;
    img{
      width: 100%;
      height: 100%;
    }
  }
}
.dialog_footer {
  text-align: center;
  // margin-top: 20px;
}
</style>

