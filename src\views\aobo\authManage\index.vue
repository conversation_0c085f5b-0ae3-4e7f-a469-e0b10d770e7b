<template>
  <div class="main-content">
    <div class="top">
      <div class="left">
        <div class="pa_name">系统管理</div>
        <div class="ch_name">/</div>
        <div class="ch_name">权限管理</div>
      </div>
    </div>
    <div class="line" />
    <div
      class="content-table"
    >
      <div class="title">
        <el-input
          v-model="keyword"
          suffix-icon="el-icon-search"
          class="input_style"
          placeholder="请输入搜索内容"
          @clear="getList(1)"
          @blur="getList(1)"
        />
        <div>
          <el-button
            type="primary"
            :disabled="!btnAuthorityList.includes('add')"
            @click="handleClick('new')"
          >新增</el-button>
        </div>
      </div>
      <el-table
        v-loading="loading"
        :header-cell-style="tableHeaderStyle"
        header-row-class-name="table-header"
        :data="tableData"
        stripe
        style="width: 100%"
        height="65vh"
        row-key="id"
      >
        <el-table-column
          label="序号"
          type="index"
          width="100"
          align="center"
        />
        <el-table-column
          prop="alarmTime"
          label="角色名称"
          show-overflow-tooltip
          align="center"
        >
          <template slot-scope="scope">
            <span>{{ scope.row.name || '--' }}</span>
          </template>
        </el-table-column>
        <el-table-column
          prop="tempVal"
          label="角色描述"
          align="center"
          show-overflow-tooltip
        >
          <template slot-scope="scope">
            <span>{{ scope.row.detail ||'--' }}</span>
          </template>
        </el-table-column>
        <el-table-column
          v-if="btnArrList.length"
          label="操作"
          fixed="right"
          align="center"
          :width="btnArrList.length * 65"
        >
          <template v-slot="scope">
            <el-button
              type="text"
              :disabled="scope.row.status === 1"
              @click="handleClick('look', scope.row)"
            >查看</el-button>
            <el-button
              type="text"
              :disabled="scope.row.status === 1 || !btnAuthorityList.includes('update')"
              :style="{color: scope.row.status === 1 || !btnAuthorityList.includes('update') ? '' : '#02C69E'}"
              @click="handleClick('edit', scope.row)"
            >编辑</el-button>
            <el-button
              type="text"
              :disabled="scope.row.status === 1 || !btnAuthorityList.includes('del')"
              :style="{color: scope.row.status === 1 || !btnAuthorityList.includes('del') ? '' : '#FF4242'}"
              @click="handleClick('del', scope.row)"
            >删除</el-button>
          </template>
        </el-table-column>
      </el-table>
      <el-pagination
        background
        :current-page.sync="pageNum"
        :page-size="pageSize"
        layout="total,prev, pager, next,sizes, jumper"
        :page-sizes="[10, 20, 50, 100]"
        :total="total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
    <!-- 删除 -->

    <el-dialog
      title="删除角色"
      :visible.sync="delDialogVisible"
      width="450px"
      :modal-append-to-body="false"
      top="30vh"
      @close="closeDelDialog()"
    >
      <span>确认删除该角色？</span>
      <div
        slot="footer"
        class="dialog_footer"
      >
        <el-button
          type="primary"
          plain
          style="margin-right:10px"
          @click="closeDelDialog()"
        >取 消
        </el-button>
        <el-button
          type="primary"
          @click="doDel()"
        >确 定
        </el-button>
      </div>
    </el-dialog>
    <!-- 新增/编辑 -->
    <el-dialog
      :title="types==='sysEdit' ? '编辑角色' : '新建角色'"
      :visible.sync="showInfo"
      :modal-append-to-body="false"
      width="70%"
      top="4vh"
      @close="close()"
    >
      <infoDetail
        v-if="showInfo"
        :types="types"
        :sys-edit-list="form"
        @close="close"
      />
    </el-dialog>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import { getList, sysRoleDel } from '@/api/auth'
import infoDetail from './modules/infoDetail'

export default {
  name: 'AlarmInfo',
  components: {
    // commonBtn
    infoDetail
  },
  data() {
    return {
      btnAuthorityList: [], // 按钮权限
      delDialogVisible: false,
      btnArrList: [1, 2, 3],
      treeList: [],
      treeIds: [],
      showInfo: false,
      types: 'sysAdd',
      defaultProps: {
        children: 'childList',
        label: 'name'
      },
      isAdd: true,
      isSelectAll: false,
      rules: {
        username: [
          { required: true, message: '请输入用户名', trigger: 'blur' }
        ],
        content: [
          { required: true, message: '请输入描述', trigger: 'blur' }
        ]
      },
      form: {
        name: '',
        coount: '',
        opreationList: []
      },
      keyword: '',
      tableData: [],
      loading: false,
      pageNum: 1,
      pageSize: 10,
      total: 0
    }
  },
  computed: {
    ...mapGetters([
      'tableHeaderStyle', 'btnAuthority'
    ])
  },
  created() {
    const cur = this.btnAuthority.find((item) => item.code === this.$route.meta.code)
    if (cur) this.btnAuthorityList = cur.functionPermissionCode
    console.log(this.btnAuthorityList, 'btnAuthorityList')
  },
  mounted() {
    this.getList(1)
  },
  methods: {
    closeDelDialog() {
      this.delDialogVisible = false
      this.form = {}
    },
    async doDel() {
      const res = await sysRoleDel(this.form.id)
      if (res.code === 200) {
        this.$message.success('删除成功')
        if (this.pageNum > 1 && this.tableData.length === 1) {
          this.pageNum--
        }
        this.delDialogVisible = false
        this.getList()
        this.form = {}
      }
    },
    getList(isPage) {
      if (isPage) {
        this.pageNum = 1
      }
      const params = {
        pageNum: this.pageNum,
        pageSize: this.pageSize,
        query: {
        },
        keyword: this.keyword
      }
      this.loading = true
      getList(params).then((res) => {
        this.tableData = res.data.records.map((item) => item)
        this.pageNum = res.data.current
        this.total = Number(res.data.total)
      }).finally(() => {
        this.loading = false
      })
    },
    // 改变每页条数
    handleSizeChange(val) {
      this.pageSize = val
      this.getList()
    },
    // 分页
    handleCurrentChange(val) {
      this.pageNum = val
      this.getList()
    },
    handleDel(e) {

    },
    close() {
      this.showInfo = false
      this.form = {}
      this.getList()
    },
    // 按钮点击事件
    handleClick(tagName, e) {
      switch (tagName) {
        case 'new':
          this.types = 'sysAdd'
          this.showInfo = true
          break
        case 'look':
          this.types = 'sysLook'
          this.form = e
          this.showInfo = true
          break
        case 'edit':
          this.types = 'sysEdit'
          this.showInfo = true
          this.form = e
          break
        case 'del':
          this.form = e
          this.delDialogVisible = true
          break
        default:
          break
      }
    }
  }

}
</script>

  <style lang="scss" scoped>
  .form-box{
    display:flex;
    align-items:left;
    flex-direction: column;
    padding: 15px 20px;
    width: 100%;
    .form-top-box{
        width: 100%;
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        align-items: center;
    }
    .form-buttom-box{
        width: 100%;
        border-radius: 4px;
        overflow-y: auto;
        border: 1px solid #E4EAF0;
        height: 164px;
    }
  }
  .main-content{
    height: calc(100%);
    padding: 30px 0;
    box-sizing: border-box;
    font-family: PingFang SC RE;
    .top {
      display: flex;
      margin-bottom: 20px;
      .left {
        display: flex;
        align-items: center;
        font-size: 17px;
        font-weight: bold;
        .pa_name {
          color: #8D95A5;
          margin-right: 5px;
        }
        .ch_name {
          color: #202225;
          margin-right: 5px;
        }
      }
    }
    .line {
      width: 100%;
      height: 1px;
      background: #E5E5E5;
      margin-bottom: 30px;
    }
    .content-table {
      width: 100%;
      height: 94%;
      position: relative;
      .title {
        display: flex;
        justify-content: space-between;
        margin-bottom: 20px;
        .input_style {
          margin-right:20px;
          width:200px;
        }
        .title_name {
          height: 20px;
          font-size: 18px;
          font-family: PingFang SC RE;
          font-weight: bold;
          color: #202225;
          padding-left: 10px;
          border-left: 4px solid #1768EB;
        }
      }
    }
    .dialog_footer {
      display: flex;
      justify-content: center;
      // margin-top: 20px;
    }
  }
  .uploadText1{
    display: flex;
    align-items: center;
    font-family: PingFang SC;
    font-weight: 500;
    font-size: 15px;
    color: #202225;
  }
  .uploadText2{
    font-family: PingFang SC;
    font-weight: 400;
    font-size: 13px;
    color: #6A6C74;
  }
  ::v-deep .el-upload__tip{
    margin-top: 0;
  }
  </style>
