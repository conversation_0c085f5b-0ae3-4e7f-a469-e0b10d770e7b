import request from '@/utils/request'

// 获取角色分页
export function getList(data) {
  return request({
    url: `/api/v1/sys/role/page`,
    method: 'post',
    data
  })
}
// 获取菜单树
export function getTree() {
  return request({
    url: '/api/v1/sys/role/menuTree',
    method: 'post'
  })
}
// 添加角色
export function sysRoleSave(data) {
  return request({
    url: `/api/v1/sys/role/add`,
    method: 'post',
    data
  })
}
// 修改角色
export function sysRoleUpdate(data) {
  return request({
    url: `/api/v1/sys/role/update`,
    method: 'post',
    data
  })
}
// 删除角色
export function sysRoleDel(id) {
  return request({
    url: `/api/v1/sys/role/del?id=${id}`,
    method: 'get'
  })
}
