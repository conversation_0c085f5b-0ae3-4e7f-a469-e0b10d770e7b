<script>
import L from 'leaflet'
import 'leaflet/dist/leaflet.css'
import '@geoman-io/leaflet-geoman-free'
import '@geoman-io/leaflet-geoman-free/dist/leaflet-geoman.css'
import { MAP_BOUNDS, MAP_OPTION } from '@/constants'
import EditName from '@/components/LocateDialog/components/EditName'
import Vue from 'vue'

let map

const icon = L.icon({
  iconUrl: require('@/assets/circle-icon.svg'),
  iconSize: [20, 20],
  iconAnchor: [10, 10]
})

const option = { }

export default {
  name: 'LocatePoint',
  props: {
    // 背景图地址
    image: {
      type: String,
      default: ''
    },
    points: {
      type: Array,
      default: () => []
    },
    // 点位数量
    pointLength: {
      type: Number,
      default: 1
    },
    // 是否是详情
    isDetail: {
      type: Boolean,
      default: false
    }

  },
  data() {
    return {
      markerList: [],
      dialogVisible: false,
      formData: null,
      rules: {
        name: [{ required: true, message: '请输入设备名称', trigger: 'blur' }],
      },

    }
  },
  mounted() {
    this.initMap()
    this.isDetail ? this.showMarker() : this.initEditor()
  },
  destroyed() {
    console.log('destroy')
  },
  methods: {
    initMap() {
      map = L.map('leaflet-map', MAP_OPTION)
      // 加载图片图层
      L.imageOverlay(this.image, MAP_BOUNDS).addTo(map)
    },
    initEditor() {
      map.pm.addControls({
        position: 'topleft',
        drawCircleMarker: false,
        cutPolygon: false,
        drawRectangle: false,
        drawText: false,
        drawCircle: false,
        drawPolygon: false,
        rotateMode: false,
        dragMode: false,
        drawPolyline: false,

      })
      map.pm.setLang('zh')
      // 激活折线绘制
      map.pm.enableDraw('Marker', option)

      // 监听新增标记
      map.on('pm:create', (e) => {
        this.markerList.push(e.layer)
        if (this.markerList.length >= this.pointLength) {
          map.pm.disableDraw()
          map.pm.Toolbar.setButtonDisabled('drawMarker', true)
        }
        this.addTooltip(e.layer)
        this.formData = {}
        this.dialogVisible = true
      })

      // 监听移除标记
      map.on('pm:remove', (shape) => {
        const index = this.markerList.findIndex((item) => item === shape.layer)
        if (index > -1) {
          this.markerList.splice(index, 1)
        }
        if (this.markerList.length < this.pointLength) {
          map.pm.Toolbar.setButtonDisabled('drawMarker', false)
          if (!this.markerList.length) {
            map.pm.enableDraw('Marker', option)
          }
        }
      })
    },
    /**
     * 详情回显marker
     * */
    showMarker() {
      this.points.forEach((item) => {
        const marker = L.marker({ lat: item.x, lng: item.y },).addTo(map)
        marker.bindTooltip(item.name, {
          permanent: true, class: 'leaflet-tooltip', interactive: false, direction: 'top', offset: [-15, -15],
        })

        this.markerList.push(marker)
      })
    },
    /**
     * 给marker添加tooltip
     * */
    addTooltip(marker) {
      const CompConstructor = Vue.extend(EditName)
      const div = document.createElement('div')
      const name = `${this.markerList.length}号点位`
      marker._data = {
        name,
      }
      const instance = new CompConstructor({
        el: div,
        propsData: { data: null, threshold: this.threshold, name },
      })
      instance.$on('change', (e) => {
        marker._data.name = e
      })
      const popupEl = instance.$el
      popupEl.addEventListener('click', (e) => {
        e.stopPropagation()
      })
      popupEl.addEventListener('mousemove', (e) => {
        e.stopPropagation()
      })
      popupEl.addEventListener('dbclick', (e) => {
        e.stopPropagation()
      })

      marker.bindTooltip(popupEl, {
        permanent: true, class: 'leaflet-tooltip', interactive: true, direction: 'top', offset: [0, -43],
      })
    },
    /**
     * 修改点位名称
     * */
    submit() {
      console.log('submit')
    },

    /**
     * 供外部调用，校验是否绘制完成
     * */
    validate() {
      if (this.markerList > this.pointLength) {
        this.$message.warning({
          message: `最多绘制${this.pointLength}个点位`
        })
        return false
      }
      /** 如果还没完成绘制则返回*/
      if (!this.markerList.length) {
        this.$message.warning({
          message: '请先完成标绘'
        })
        return false
      }
      return true
    },

    /**
     * 外部获取点位
     * */
    getPoints() {
      return this.markerList.map((item) => ({ ...item.getLatLng(), name: item._data.name }))
    },

  }
}
</script>

<template>
  <div>
    <div
      v-if="!isDetail"
      class="title"
    >
      最多标绘
      <span class="count">{{ pointLength }}</span>
      个点位
    </div>
    <div id="leaflet-map" />
  </div>
</template>

<style scoped lang="scss">
#leaflet-map {
  width: 1200px;
  height: 70vh;
}

.title {
  font-size: 16px;
  margin-bottom: 15px;
  .count {
    font-weight: bold;
  }
}

::v-deep .leaflet-tooltip {
  padding: 5px 8px!important;
  background: white;
  border-radius: 4px;
  text-align: center;
}

</style>

