const state = {
  // 模型是否加载完成
  loaded: false,
  progress: 0,
}

const mutations = {
  SET_LOADED: (state, value) => {
    state.loaded = value
  },
  SET_PROGRESS: (state, value) => {
    state.progress = value
  },
}

const actions = {
  setLoaded({ commit }, value) {
    commit('SET_LOADED', value)
  },
  setProgress({ commit }, value) {
    commit('SET_PROGRESS', value)
  },
}

export default {
  namespaced: true,
  state,
  mutations,
  actions,
}
