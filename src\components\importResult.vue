<template>
  <div class="import-box">
    <div class="image">
      <img :src="resultData.successTotal?imagesS:imagesF">
    </div>
    <div class="text">
      导入{{ resultData.successTotal?'成功':'失败' }}
    </div>
    <p
      v-if="resultData.successTotal==0&&resultData.failTotal==0"
      class="msg"
      style="margin-bottom:40px"
    >抱歉，本次导入为空数据，请修改后重新导入</p>
    <div v-else>
      <p class="msg">本次成功导入{{ resultData.successTotal }}条数据，导入失败{{ resultData.failTotal }}条数据</p>
      <p class="tips">注：如有导入失败数据，请查看源数据修改后再次导入</p>
      <div
        v-if="resultData.failTotal>0"
        class="down"
        @click="downFail"
      >
        <span class="downText">失败数据下载</span>
        <img
          class="downImg"
          src="../assets/tc_chengg.png"
        >
      </div>
    </div>
    <div
      slot="footer"
      class="form-header foot"
    >
      <el-button
        class="cancel-btn"
        @click="handleClose"
      >取 消</el-button>
      <el-button
        class="cofirm-btn"
        type="primary"
        @click="submitForm"
      >确 定</el-button>
    </div>
  </div>
</template>
<script>
export default {
  name: 'ImportResult',
  components: {},
  props: {
    resultData: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      imagesS: require('@/assets/icon_xai.png'),
      imagesF: require('@/assets/icon_guanbi.png')
    }
  },
  methods: {
    handleClose() {
      this.$emit('handleClose')
    },
    submitForm() {
      this.$emit('submitForm')
    },
    downFail() {
      this.$emit('downFail')
    },
    // 预览图片
    handlePictureCardPreview(file) {
      this.urlList = []
      this.url = file.url
      this.urlList.push(file.url)
      this.$refs.previewImage.showViewer = true
    }
  }
}
</script>
<style lang="scss" scoped>
.import-box{
  margin: 0 40px;
  text-align: center;
  .image{
    width: 138px;
    height: 80px;
    margin: auto;
  }
  .text{
    font-size: 20px;
    font-family: Source Han Sans CN;
    font-weight: 500;
    color: #011831;
    line-height: 30px;
    margin-top: 20px;
  }
  .msg{
    font-size: 16px;
    font-family: Source Han Sans CN;
    font-weight: 500;
    color: #505050;
    line-height: 16px;
  }
  .tips{
    font-size: 15px;
    font-family: Source Han Sans CN;
    font-weight: 500;
    color: #F95353;
    line-height: 16px;
  }
  .down{
    width: 40%;
    margin-left: 32%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    .downText{
      font-size: 16px;
      font-family: Source Han Sans CN;
      font-weight: 500;
      text-decoration: underline;
      color: #348EF4;
      line-height: 36px;
      margin-right: 8px;
    }
    .downImg{
      width: 15px;
      height: 17px;
    }
  }
  .form-header{
    margin-top: 28px;
    margin-bottom: 18px;
  }

}

</style>
