<template>
  <div>
    <div>
      <div class="right_item">
        <div style="display: flex; flex-direction: row; align-items: center; height: 80px;">
          <div class="first">
            <div style="color: #ff4444">*</div>
            <div style="color: #5a5a5a; ">角色名称</div>
            <div class="sysInput">
              <el-input
                v-model="sysList.name"
                :disabled="types === 'sysLook'"
                placeholder="请输入内容"
                :maxlength="10"
              />
            </div>
          </div>
          <div
            class="first"
            style=""
          >
            <div style="color: #ff4444">*</div>
            <div style="color: #5a5a5a;">角色描述</div>
            <div class="textarea">
              <el-input
                v-model="sysList.detail"
                :disabled="types === 'sysLook'"
                placeholder="请输入角色描述"
                :maxlength="30"
                @input="jiaoseInput"
              />
            </div>
          </div>
        </div>

        <div class="right_title">
          权限配置
        </div>
        <div class="right_content">
          <div class="right_tree">
            <el-tree
              ref="tree"
              :data="treeList"
              show-checkbox
              :props="defaultProps"
              node-key="id"
              :default-checked-keys="menuIdList"
              default-expand-all
              @check="currentChecked"
            />
          </div>
          <div class="right_detail">
            <div
              v-for="item in authorityList"
              :key="item.menuId"
              class="menu_item"
            >
              <template v-if="item.isShow">
                <div class="menu_item_title">{{ item.name }}</div>
                <el-checkbox-group
                  v-model="item.functionPermission"
                  :disabled="types === 'sysLook'"
                >
                  <el-checkbox
                    v-for="item1 in item.functionPermissionList"
                    :key="item1.id"
                    :label="item1.id"
                  >
                    {{ item1.description }}
                  </el-checkbox>
                </el-checkbox-group>
              </template>
            </div>
          </div>
        </div>
      </div>
      <div
        style="
              display: flex;
              align-items: center;
              justify-content: center;
              margin-top: 50px
            "
      >
        <div
          class="qx"
          @click="qx"
        >取消</div>
        <div
          class="qd"
          @click="qd"
        >确定</div>
      </div>

    </div>
  </div>
</template>

<script>
import { deepClone } from '@/utils/index'
import { getTree, sysRoleSave, sysRoleUpdate } from '@/api/auth'
// import {
//   sysRoleSave, sysRoleUpdate, tree
// } from '@/api/aobo/rightsManagement/roleManagement'

export default {
  components: {},
  props: {
    types: {
      type: String,
      default: 'sysAdd'
    },

    sysEditList: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      // defaultProps: {
      //   children: 'child',
      //   label: 'authorityName',
      //   disabled: this.disabledFn
      // },
      defaultProps: {
        children: 'childList',
        label: 'name',
        disabled: this.disabledFn
      },
      treeList: [],
      sysList: {
        name: '',
        detail: '',
        deptIds: ''
      },
      authorityList: [],
      menuIdList: [],
      treeData: [] // 部门树
    }
  },
  computed: {},
  watch: {},
  created() {
    this.roleDropdowns()
  },
  mounted() {
    if (this.types === 'sysEdit' || this.types === 'sysLook') {
      this.sysList = this.sysEditList
      // 权限配置树回显
      this.menuIdList = this.sysEditList.permissionJson.map((item) => item.menuId)
    }
    setInterval(() => {
      document.querySelectorAll('.el-cascader-node__label').forEach((el) => {
        // eslint-disable-next-line func-names
        el.onclick = function() {
          if (this.previousElementSibling) this.previousElementSibling.click()
        }
      })
    }, 1000)
  },
  methods: {

    // 去除空childList字段
    checkChildList(arr) {
      arr.forEach((item) => {
        if (!item.childList.length) {
          delete item.childList
        } else {
          this.checkChildList(item.childList)
        }
      })
      return arr
    },
    // 角色描述长度限制
    jiaoseInput(i) {
      if (i.length > 30) {
        this.$message.warning({
          duration: 2000,
          message: '角色描述长度不能超过30,请重新填写'
        })
        // this.sysList.detail = ''
      }
    },
    // 角色名称长度限制
    inputss(i) {
      if (i.length > 10) {
        this.$message.warning({
          duration: 2000,
          message: '角色名称长度不能超过10,请重新填写'
        })
      }
    },
    // eslint-disable-next-line consistent-return
    disabledFn() {
      if (this.types === 'sysLook') {
        return true
      }
    },
    // 权限配置树列表
    async roleDropdowns() {
      const res = await getTree()
      if (res.code === 200) {
        this.treeList = res.data
        console.log(this.treeList)
        // 权限配置按钮列表回显
        if (this.types === 'sysEdit' || this.types === 'sysLook') {
          this.getSelectTreeData(this.treeList)
          this.authorityListBack(res.data)
        }
      }
    },
    // 权限配置树回显
    authorityListBack(data) {
      data.forEach((item) => {
        if (this.menuIdList.includes(item.id) && item.childList.length) {
          const idx = this.menuIdList.indexOf(item.id)
          this.menuIdList.splice(idx, 1)
        }
        if (item.childList.length) {
          this.authorityListBack(item.childList)
        }
      })
    },
    // 权限配置按钮列表回显
    getSelectTreeData(arr) {
      arr.forEach((item) => {
        if (this.menuIdList.includes(item.id)) {
          const obj = this.sysEditList.permissionJson.find((i) => i.menuId === item.id)
          // this.authorityList.push({
          //   menuId: item.id,
          //   name: item.name,
          //   functionPermissionList: item.functionPermissionList,
          //   functionPermission: obj.functionPermission,
          //   isShow: !item.childList.length && item.functionPermissionList.length
          // })
          // 父节点也要显示
          this.authorityList.push({
            menuId: item.id,
            name: item.name,
            functionPermissionList: item.functionPermissionList,
            functionPermission: obj.functionPermission,
            isShow: item.functionPermissionList.length
          })
        }
        if (item.childList) {
          this.getSelectTreeData(item.childList)
        }
      })
    },
    // 选择事件
    currentChecked(nodeObj, SelectedObj) {
      const select = SelectedObj.checkedNodes.concat(SelectedObj.halfCheckedNodes)
      const arrCopy = deepClone(this.authorityList)
      this.authorityList = []
      this.getAuthorityList(select, arrCopy)
    },
    // 获取权限列表
    getAuthorityList(arr, arrCopy) {
      arr.forEach((item) => {
        const obj = arrCopy.find((item1) => item1.menuId === item.id)
        if (obj) {
          this.authorityList.push(obj)
          return
        }
        // 如果有childList，说明是父节点，不设置按钮权限
        // if (item.childList.length) {
        //   this.authorityList.push({
        //     menuId: item.id,
        //     name: item.name,
        //     functionPermission: [],
        //     isShow: !item.childList.length
        //   })
        // } else {
        //   // 为子级且有按钮数组
        //   if (item.functionPermissionList.length) {
        //     this.authorityList.push({
        //       menuId: item.id,
        //       name: item.name,
        //       functionPermissionList: item.functionPermissionList,
        //       functionPermission: item.functionPermissionList.map(item1 => item1.id),
        //       isShow: !item.childList.length
        //     })
        //   } else {
        //     // 为子级且没有按钮数组，则不设置按钮权限
        //     this.authorityList.push({
        //       menuId: item.id,
        //       name: item.name,
        //       functionPermission: [],
        //       // isShow: !item.childList.length
        //       isShow: false
        //     })
        //     console.log(this.authorityList, 'this.authorityList')
        //   }
        // }
        // 父节点也要显示
        this.authorityList.push({
          menuId: item.id,
          name: item.name,
          functionPermissionList: item.functionPermissionList,
          functionPermission: item.functionPermissionList.map((item1) => item1.id) || [],
          isShow: item.functionPermissionList.length
        })
      })
    },
    // 取消
    qx() {
      this.$emit('close', false)
      this.sysList = {
        name: '',
        detail: '',
        deptIds: ''
      }
    },
    // 确定
    qd() {
      if (this.sysList.name === '') {
        this.$message.warning({
          duration: 2000,
          message: '角色名称不能为空'
        })
      } else if (!this.authorityList.length) {
        this.$message.warning({
          duration: 2000,
          message: '请配置权限'
        })
      } else if (this.types === 'sysAdd') {
        const data = {
          name: this.sysList.name,
          detail: this.sysList.detail,
          // deptIds: this.sysList.deptIds,
          permissionJson: this.authorityList.map((item) => ({
            menuId: item.menuId,
            functionPermission: item.functionPermission
          }))
        }
        sysRoleSave(data).then((res) => {
          if (res.code === 200) {
            this.$message.success('成功')
            this.$emit('close', 'success')
            // this.$parent.webPageLists(this.list1)
          }
        })
      } else if (this.types === 'sysEdit') {
        this.$confirm('该操作会强制相关用户下线，继续操作吗？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          closeOnClickModal: false,
          type: 'warning'
        }).then(() => {
          const data = {
            id: this.sysEditList.id,
            name: this.sysList.name,
            detail: this.sysList.detail,
            // deptIds: this.sysList.deptIds,
            permissionJson: this.authorityList.map((item) => ({
              menuId: item.menuId,
              functionPermission: item.functionPermission
            }))
          }
          sysRoleUpdate(data).then((res) => {
            if (res.code === 200) {
              this.$message.success('成功')
              this.$emit('close', 'success')
              // this.$parent.webPageLists(this.list1)
            }
          })
        })
      } else {
        this.$emit('close', 'success')
        // this.$parent.webPageLists(this.list1)
      }
    }
  }
}
</script>

  <style scoped lang="scss">
  .first {
    display: flex;
    align-items: center;
    font-size: 16px;
    margin-left: 15px;
  }
  .sysInput {
    width: 248px;
    margin-left: 7px;
  }
  .textarea {
    width: 400px;
    margin-left: 7px;
  }
  .qx {
    width: 110px;
    height: 38px;
    background: rgba(166, 175, 179, 0.15);
    //   opacity: 0.15;
    border-radius: 6px;
    line-height: 38px;
    text-align: center;
    color: #616161;
    margin-right: 20px;
    cursor: pointer;
  }
  .qd {
    width: 110px;
    height: 38px;
    // background: #c02c38;
    background: #408de8;
    border-radius: 6px;
    color: #ffffff;
    line-height: 38px;
    text-align: center;
    cursor: pointer;
  }
  .leftt {
    width: 400px;
    height: 660px;
  }
  .right_item {
    // height: 600px;
    flex: 1;
    border: 1px solid #F0F0F0;

    .right_title {
      height: 65px;
      line-height: 65px;
      padding-left: 20px;
      font-size: 18px;
      font-weight: bold;
      background: #F7F7F7;
    }
    .right_content {
      display: flex;
      .right_tree {
        width: 350px;
        height: 480px;
        overflow: auto;
        padding: 20px;
        border-right: 1px solid #F0F0F0;
      }
      .right_detail {
        flex: 1;
        height: 480px;
        overflow: auto;
        padding: 10px 30px;
        .menu_item {
          margin-bottom: 20px;
          .menu_item_title {
            font-family: PingFangSC-Regular;
            margin-bottom: 10px;
          }
        }
      }
    }
  }
  </style>
  <style lang="scss">
  .el-popper.el-cascader__dropdown.department-tree {
    .el-radio {
      display: none !important;
    }
  }
  </style>
