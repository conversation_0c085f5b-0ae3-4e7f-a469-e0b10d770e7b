<template>
  <div
    :id="id"
    :style="{ height: height, width: width }"
  />
</template>
<script>
import elementResizeDetectorMaker from 'element-resize-detector'
import chartsMixIn from './mixins'

export default {
  name: 'PieChart',
  mixins: [chartsMixIn],
  props: {
    id: {
      require: false,
      type: String,
      default: 'charts'
    },
    width: {
      require: false,
      type: String,
      default: '100%'
    },
    height: {
      require: false,
      type: String,
      default: '100%'
    },
    propData: {
      require: false,
      type: Array,
      default: () => []
    },
    propDataType: {
      require: false,
      type: Array,
      default: () => []
    },
    propDataNum: {
      require: false,
      type: Array,
      default: () => []
    }
  },
  watch: {
    propData(newValue) {
      if (newValue) {
        // this.hideLoading()
        if (newValue.length < 1) {
          this.noData()
        }
        if (this.chart) {
          this.chart.clear()
          this.$nextTick(() => {
            this.initChart()
          })
          // console.log(this.propData, newValue)
        }
        this.$nextTick(() => {
          this.initChart()
        })
      }
    },
    deep: true
  },
  created() {
    this.doNotRedraw = true
  },
  mounted() {
    this.initChart()
    // console.log(this.propData, 888888888)
    const erd = elementResizeDetectorMaker()
    erd.listenTo(document.getElementById(this.id), (element) => {
      const width = element.offsetWidth
      const height = element.offsetHeight
      this.$nextTick(() => {
        console.log(`Size: ${width}x${height}`)
        // 使echarts尺寸重置
        this.chart = this.$echarts.init(document.getElementById(this.id)).resize()
      })
    })
  },
  methods: {
    initChart() {
      this.chart = this.$echarts.init(document.getElementById(this.id))
      const data = this.propData
      // console.log(this.propData2)
      this.option = {
        tooltip: {
          trigger: 'item',
          // formatter: '{a} <br/>{b}: {c} ({d}%)'
          formatter(param) {
            return `${param.marker}` +
            `&nbsp;&nbsp;${param.name}：${param.value}&nbsp;&nbsp;(${param.data.label}%)<br>`
          }
        },
        legend: {
          // 取消图例上的点击事件
          selectedMode: false,
          orient: 'vertical',
          right: 'left',
          y: 20,
          data: this.propDataType,
          padding: 20,
          // icon: 'circle',
          formatter(name) {
            let target
            for (let i = 0; i < data.length; i++) {
              if (data[i].name === name) {
                target = data[i].label
              }
            }
            // const arr = [`{a|${name}}`, `{b| ${target}}`]
            const arr = [`{a|${name}   |   ${target}%}`]
            return arr.join('\n')
          },
          textStyle: {
            rich: {
              a: {
                align: 'center',
                fontSize: 14,
                color: '#505050',
                padding: [20, 15, 20, 8]
              }
            }
          }
        },
        series: [
          {
            name: '访问来源',
            type: 'pie',
            radius: ['50%', '70%'],
            center: ['26%', '50%'],
            avoidLabelOverlap: false,
            itemStyle: {
              borderRadius: 3,
              borderColor: '#fff',
              borderWidth: 2
            },
            label: {
              normal: {
                show: false,
                position: 'center',
                // eslint-disable-next-line no-shadow
                formatter(data) { // 设置圆饼图中间文字排版
                  return `${data.name}\n${data.value}人`
                }
              },
              emphasis: {
                show: true, // 文字至于中间时，这里需为true
                textStyle: { // 设置文字样式
                  fontSize: '16',
                  color: '#0061ce',
                  lineHeight: 24
                }
              }
            },
            labelLine: {
              normal: {
                show: false
              }
            },
            data: this.propData
          }
        ]
      }
      this.chart.setOption(this.option)
      // 设置默认选值第一个为高亮
      // this.chart.dispatchAction({ type: 'highlight', seriesIndex: 0, dataIndex: 0 })
      this.chart.dispatchAction({ type: 'highlight', seriesIndex: 0, dataIndex: 0 })

      this.chart.on('mouseover', (e) => {
        // 当检测到鼠标悬停事件，不等于第一个（自身）时，取消默认选中高亮
        if (e.dataIndex !== 0) {
          this.chart.dispatchAction({
            type: 'downplay',
            seriesIndex: 0,
            dataIndex: 0
          })
        }
        // 高亮显示悬停的那块
        this.chart.dispatchAction({
          type: 'highlight',
          seriesIndex: 1,
          dataIndex: e.dataIndex
        })
      })
      // 检测鼠标移出后显示之前默认高亮的那块
      this.chart.on('mouseout', (e) => {
        this.chart.dispatchAction({
          type: 'highlight',
          seriesIndex: 0,
          dataIndex: 0
        })
      })
    }
  }
}
</script>
<style scoped>
</style>
