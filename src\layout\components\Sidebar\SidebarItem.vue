<template>
  <div v-if="!item.hidden">
    <template
      v-if="hasOneShowingChild(item.children,item) &&
        (!onlyOneChild.children||onlyOneChild.noShowingChildren)&&
        !item.alwaysShow"
    >
      <app-link
        v-if="onlyOneChild.meta"
        :to="resolvePath(onlyOneChild.path)"
        :selected="true"
      >
        <el-menu-item
          :index="resolvePath(onlyOneChild.path)"
          :class="{'submenu-title-noDropdown':!isNest}"
        >
          <item
            :icon-image="item.meta && item.meta.iconImage"
            :title="onlyOneChild.meta.title"
            :is-active="item.meta.isActive"
          />
        </el-menu-item>
      </app-link>
    </template>

    <el-submenu
      v-else
      ref="subMenu"
      :index="resolvePath(item.path)"
      popper-append-to-body
      :selected="true"
    >
      <template slot="title">
        <item
          v-if="item.meta"
          :icon="item.meta && item.meta.icon"
          :icon-image="item.meta && item.meta.iconImage"
          :title="item.meta.title"
          :is-active="item.meta.isActive"
        />
      </template>
      <sidebar-item
        v-for="child in item.children"
        :key="child.path"
        :is-nest="true"
        :item="child"
        :base-path="resolvePath(child.path)"
        class="nest-menu"
      />
    </el-submenu>
  </div>
</template>

<script>
import path from 'path'
import { isExternal } from '@/utils/validate'
import Item from './Item'
import AppLink from './Link'
import FixiOSBug from './FixiOSBug'

export default {
  name: 'SidebarItem',
  components: { Item, AppLink },
  mixins: [FixiOSBug],
  props: {
    // route object
    item: {
      type: Object,
      required: true
    },
    isNest: {
      type: Boolean,
      default: false
    },
    basePath: {
      type: String,
      default: ''
    }
  },
  data() {
    // To fix https://github.com/PanJiaChen/vue-admin-template/issues/237
    // TODO: refactor with render function
    this.onlyOneChild = null
    return {}
  },
  methods: {
    hasOneShowingChild(children = [], parent) {
      const showingChildren = children.filter((item) => {
        if (item.hidden) {
          return false
        }
        // Temp set(will be used if only has one showing child)
        this.onlyOneChild = item
        return true
      })

      // When there is only one child router, the child router is displayed by default
      if (showingChildren.length === 1 && (!parent.meta || !parent.meta.title)) {
        return true
      }

      // Show parent if there are no child router to display
      if (showingChildren.length === 0) {
        this.onlyOneChild = { ...parent, path: '', noShowingChildren: true }
        return true
      }

      return false
    },
    resolvePath(routePath) {
      if (isExternal(routePath)) {
        return routePath
      }
      if (isExternal(this.basePath)) {
        return this.basePath
      }
      return path.resolve(this.basePath, routePath)
    }
  }
}
</script>
<style lang="scss">
.el-menu {
  background: #fff !important;
}
.el-menu-item {
  padding: 0 !important;
  margin-left: 15px !important;
  height: 100px !important;
  width: 120px !important;
  text-align: center;
  background: #fff !important;
}
.nest-menu {
  .el-menu-item {
    height: 50px !important;
    width: 50px !important;
  }
}
.el-submenu__title {
  padding: 0 !important;
  margin-left: 15px !important;
  height: 100px !important;
  width: 120px !important;
  text-align: center;
  background: #fff !important;
}
#app .sidebar-container .el-submenu .el-menu-item {
  margin-left: 15px !important;
  &:hover {
    margin-left: 15px !important;
  }
}
#app .sidebar-container .is-active > .el-submenu__title {
  margin-left: 15px !important;
}
.el-submenu__icon-arrow {
  top: 40px !important;
  right: 5px !important;
  font-size: 17px !important;
  font-weight: bold !important;
}
</style>
