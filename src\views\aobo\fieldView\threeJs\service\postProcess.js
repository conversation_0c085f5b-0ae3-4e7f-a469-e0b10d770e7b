import { EffectComposer } from 'three/examples/jsm/postprocessing/EffectComposer'
import { RenderPass } from 'three/examples/jsm/postprocessing/RenderPass'
import { OutputPass } from 'three/examples/jsm/postprocessing/OutputPass'
import { OutlinePass } from 'three/examples/jsm/postprocessing/OutlinePass'
import * as THREE from 'three'
import { ShaderPass } from 'three/examples/jsm/postprocessing/ShaderPass'
import { FXAAShader } from 'three/examples/jsm/shaders/FXAAShader'
import {
  camera, canvasSize, renderer, scene
} from '@/views/aobo/fieldView/threeJs/service/core'

export class PostProcess {
  outlinePass

  composer

   initialized = false

   constructor() {}

   init() {
     if (this.initialized) return
     this.initialized = true
     const composer = new EffectComposer(renderer)
     this.composer = composer
     const renderPass = new RenderPass(scene, camera)
     composer.addPass(renderPass)

     /** 重要，避免后处理影响原场景的显示*/
     const outputPass = new OutputPass()
     composer.addPass(outputPass)

     /** 边框效果*/
     const outlinePass = new OutlinePass(new THREE.Vector2(canvasSize.width, canvasSize.height), scene, camera)
     this.outlinePass = outlinePass
     this.resetOutlinePass()
     outlinePass.visibleEdgeColor.set('#5176A9') // 包围线颜色
     outlinePass.hiddenEdgeColor.set('green') // 被遮挡的边界线颜色
     composer.addPass(outlinePass)

     /** 抗锯齿*/
     const effectFXAA = new ShaderPass(FXAAShader)
     const pixelRatio = renderer.getPixelRatio()
     effectFXAA.uniforms.resolution.value.set(1 / (canvasSize.width * pixelRatio), 2 / (canvasSize.height * pixelRatio))
     effectFXAA.renderToScreen = true
     effectFXAA.needsSwap = true
     composer.addPass(effectFXAA)
   }

   /** 重置边框效果*/
   resetOutlinePass() {
     const { outlinePass } = this
     outlinePass.edgeStrength = 5 // 包围线浓度
     outlinePass.edgeGlow = 4 // 边缘线范围
     outlinePass.edgeThickness = 10 // 边缘线浓度
     outlinePass.pulsePeriod = 10 // 包围线闪烁频率
   }
}
