<script>
export default {
  name: 'EditName',
  props: {
    name: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      defaultName: this.name,
      formData: {
        name: this.name,
      },
      rules: {
        name: [{ required: true, message: ' ', trigger: 'blur' }],
      },
    }
  },
  mounted() {
    console.log(this.$refs.inputRef.select)
    this.$refs.inputRef.select()
  },
  methods: {
    validate() {
      this.$refs.formRef.validate().then(() => {
        console.log('test')
      })
    },
    change() {
      if (!this.formData.name) {
        this.formData.name = this.defaultName
      }
      this.$emit('change', this.formData.name)
    }
  }
}
</script>

<template>
  <el-form
    ref="formRef"
    :model="formData"
    :rules="rules"
    size="small"
    @submit.prevent
  >
    <el-form-item
      label=""
      prop="name"
    >
      <el-input
        ref="inputRef"
        v-model.trim="formData.name"
        placeholder="请输入点位名称"
        style="width:110px;"
        @change="change"
      />
    </el-form-item>
  </el-form>
</template>

<style scoped lang="scss">
::v-deep .el-form-item {
  margin-bottom: 0;
}

</style>
