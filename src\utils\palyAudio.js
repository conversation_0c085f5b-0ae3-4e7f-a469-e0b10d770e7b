/**
 * 该函数播放一个音频文件，如果你想让它循环播放，你可以指定一个循环间隔时间
 * @param {RequestInfo | URL} musicFile - 您要播放的文件
 * @param {number} [loopCount] - 循环播放次数，如果传入-1，则表示无限循环
 * @returns 返回音频源(bufferSource)
 */
export function playAudio(musicFile, loopCount = 0) {
  let source,
    audioCtx
  let count = 0

  let timer

  let dispose

  const play = async() => {
    const AudioContext = window.AudioContext || window.webkitAudioContext
    audioCtx = new AudioContext()
    source = audioCtx.createBufferSource()
    const myRequest = new Request(musicFile)
    const response = await fetch(myRequest)
    const buffer = await response.arrayBuffer()
    await audioCtx.decodeAudioData(buffer, (decodedData) => {
      source.buffer = decodedData
      source.connect(audioCtx.destination)
    })
    source.start()

    // 销毁
    dispose = () => {
      clearTimeout(timer)
      source.stop()
      audioCtx.close()
    }

    source.onended = () => {
      count++
      dispose()
      // 如果传入了循环间隔时间，则设置循环播放
      if (count < loopCount || loopCount === -1) {
        timer = setTimeout(() => {
          play()
        }, 300)
      }
    }
  }

  play()

  const getDispose = () => {
    dispose()
  }

  return getDispose
}
