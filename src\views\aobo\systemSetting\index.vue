<script>
import { mapGetters } from 'vuex'
import UploadSingleImage from '@/components/UploadImage/UploadSingleImage'
import { updateWebInfo } from '@/api/user'

export default {
  name: 'SystemSetting',
  components: { UploadSingleImage },
  data() {
    return {
      formData: {
        webTitle: this.$store.state.settings.webTitle,
        logo: this.$store.state.settings.logo,
        loginBackground: this.$store.state.settings.loginBg,
        singleLineRetainTime: 0,
        favicon: ''
      },
      loading: false
    }
  },
  computed: {
    ...mapGetters([
      'tableHeaderStyle', 'btnAuthority'
    ])
  },
  watch: {
    '$store.state.settings.loginBg': {
      handler(val) {
        this.formData = {
          ...this.$store.state.settings.systemInfo,
          webTitle: this.$store.state.settings.webTitle,
          logo: this.$store.state.settings.logo,
          loginBackground: val,
          singleLineRetainTime: this.$store.state.settings.singleLineRetainTime,
          favicon: this.$store.state.settings.favicon
        }
      },
      immediate: true
    }
  },

  mounted() {
  },
  methods: {
    submit() {
      this.loading = true
      updateWebInfo(this.formData).then(() => {
        this.$message.success('保存成功')
        window.location.reload()
      }).finally(() => {
        this.loading = false
      })
    }

  }
}
</script>

<template>
  <div
    v-loading="loading"
    class="main-content"
  >
    <div class="top">
      <div class="left">
        <div class="pa_name">系统管理</div>
        <div class="ch_name">/</div>
        <div class="ch_name">页面定制</div>
      </div>
    </div>
    <div class="line" />
    <el-form
      :model="formData"
      label-width="100px"
      label-position="right"
    >
      <el-form-item label="系统标题">
        <el-input
          v-model="formData.webTitle"
          style="width: 200px"
        />
      </el-form-item>
      <el-form-item label="系统Logo">
        <UploadSingleImage
          v-model="formData.logo"
          style="--upload-size: 108px;"
        />
      </el-form-item>
      <el-form-item label="登录背景图">
        <UploadSingleImage v-model="formData.loginBackground" />
      </el-form-item>
      <el-form-item label="数据保留时长">
        <el-input-number
          v-model="formData.singleLineRetainTime"
          :min="0"
        />
        <span style="margin-left: 6px;">单点曲线数据保留期限（小时）</span>
      </el-form-item>
      <el-form-item
        label="位移历史数据保留期限"
        label-width="auto"
      >
        <el-input-number
          v-model="formData.moveHistoryRetainDay"
          :min="0"
        />
        <span style="margin-left: 6px;">天</span>
      </el-form-item>
      <el-form-item
        label="温度历史数据保留期限"
        label-width="auto"
      >
        <el-input-number
          v-model="formData.tempHistoryRetainDay"
          :min="0"
        />
        <span style="margin-left: 6px;">天</span>
      </el-form-item>
    </el-form>
    <div class="bottom">
      <el-button
        type="primary"
        class="btn"
        @click="submit"
      >确定</el-button>
    </div>

  </div>
</template>

<style lang="scss" scoped>
.main-content {
  padding-top: 30px;
  font-family: PingFang SC RE;
  .top {
    display: flex;
    margin-bottom: 20px;
    .left {
      display: flex;
      align-items: center;
      font-size: 17px;
      font-weight: bold;
      .pa_name {
        color: #8D95A5;
        margin-right: 5px;
      }
      .ch_name {
        color: #202225;
        margin-right: 5px;
      }
    }
  }
  .line {
    width: 100%;
    height: 1px;
    background: #E5E5E5;
    margin-bottom: 20px;
  }
  .bottom {
    display: grid;
    place-items: center;
    width: 100%;
    .btn {
      width: 200px;
      letter-spacing: 16px;
      text-align: center;

    }
  }

}

</style>

