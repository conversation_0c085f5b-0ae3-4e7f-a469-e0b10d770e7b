
import request from '@/utils/request'

// 项目点列表
export function projectDownList(params) {
  return request({
    url: '/api/v1/web/projectPoint/doDropDownList',
    method: 'get',
    params
  })
}

// 获取账号（责任人）下拉
export function dropdownAccount(params) {
  return request({
    url: '/api/v1/web/account/dropdown',
    method: 'get',
    params
  })
}

// 获取设备下拉框(根据项目id获取)
export function deviceDropdown(params) {
  return request({
    url: 'api/v1/web/deviceManage/deviceDropdown',
    method: 'get',
    params
  })
}

// 获取设备下拉框（无项目id）
export function doDropDownList(paams) {
  return request({
    url: 'api/v1/web/deviceManage/doDropDownList',
    method: 'get'
  })
}

// 获取#号列表(根据项目id获取)
export function doDropDownWellheadList(params) {
  return request({
    url: '/api/v1/web/projectPoint/doDropDownWellheadList',
    method: 'get',
    params
  })
}

// 设备类型
export function doDropDownEquipmentTypeList(paams) {
  return request({
    url: '/api/v1/web/deviceTypeManage/doDropDownList',
    method: 'get'
  })
}

