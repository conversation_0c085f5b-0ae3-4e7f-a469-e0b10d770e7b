import request from '@/utils/request'

// 修改个人信息
export function updateUser(data) {
  return request({
    url: '/api/v1/sys-user/update-cable',
    method: 'put',
    data
  })
}

// 修改密码
export function updatePassword(data) {
  return request({
    url: '/api/v1/sys-user/update-password',
    method: 'put',
    data
  })
}
// 修改密码
export function renewPassword(data) {
  return request({
    url: '/api/v1/web/manage/renewPassword',
    method: 'put',
    data
  })
}
