<template>
  <div class="third_page">
    <div class="third_page_main">
      <section class="row">
        <div
          v-for="item in row1"
          :key="item.id"
          class="item"
          :class="getClass(item)"
        >
          <ThirdPopup
            v-if="item.bound"
            :data="item"
            :threshold="threshold"
          />
        </div>
      </section>
      <section class="row">
        <div
          v-for="item in row2"
          :key="item.id"
          class="item"
          :class="getClass(item)"
        >
          <ThirdPopup
            v-if="item.bound"
            :data="item"
            :threshold="threshold"
          />
        </div>

      </section>
    </div>
  </div>
</template>

<script>
import ThirdPopup from './thirdPopup.vue'

export default {
  name: 'ThirdPage',
  components: { ThirdPopup },
  props: {
    list: {
      type: Array,
      default: () => []
    },
    threshold: Number,
    current: Object
  },
  computed: {
    row1() {
      return this.list.slice(0, this.list.length / 2)
    },
    row2() {
      return this.list.slice(this.list.length / 2)
    }
  },
  methods: {
    getClass(data) {
      let classStr = data.temperature && data.temperature.value > this.threshold ? 'alarm_container' : 'normal_container'
      if (this.current && data.cabinetStr === this.current.cabinetStr && data.cellStr === this.current.cellStr) classStr += ' animate'
      return classStr
    }
  }
}
</script>

<style scoped lang="scss">
.third_page {
  position: relative;
  width: 100%;
  height: 100%;
  display: grid;
  place-items: center;
  z-index: 9;
}
.third_page_main {
  display: grid;
  min-width: 627px;
  min-height: 465px;
  max-width: 70vw;
  grid-template-rows: 1fr 1fr;
  gap: 3px 0;
  border-color: #253857;
  border-style: solid;
  border-width: 13px;
  overflow-x: auto;
  scrollbar-width: thin;
  scrollbar-color: #253857 #42618D;

}
.row {
  display: flex;
  gap: 0 3px;
  border: 2px solid #42618D;
  padding: 6px;
  .item {
    position: relative;
    display: grid;
    place-items: center;
    width: 95px;
    height: 22vh;
    background-size: 100% 100%;
    background-repeat: no-repeat;
    background-image: url(~@/assets/page/common_bg.webp);
    &::after {
      content: '';
      position: absolute;
      inset: 0;
      background-size: 100% 100%;
      background-repeat: no-repeat;
    }
  }
  .normal_container {
    &.animate::after {
      background-image: url(~@/assets/page/normal_bg.webp);
      animation: bg 1s infinite alternate;
    }
  }
  .alarm_container {
    &::after {
      background-image: url(~@/assets/page/alarm_bg.webp);
    }
    &.animate::after {
      animation: bg 1s infinite alternate;
    }
  }

  @keyframes bg {
    0% {
      opacity: 0;
    }
    100% {
      opacity: 1;
    }
  }
}
</style>
