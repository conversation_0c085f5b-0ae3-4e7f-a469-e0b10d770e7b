// eslint-disable-next-line import/no-cycle
import request from '@/utils/request'

// 登录
export function login(data) {
  return request({
    url: '/api/v1/auth/login',
    // url: '/api/v1/auth/login',
    method: 'post',
    data
  })
}
// 退出
export function logout() {
  return request({
    url: '/api/v1/auth/logout?type=account',
    // url: '/api/v1/logout/web',
    method: 'get'
  })
}
// 获取验证码
export function getValidateCode(times) {
  return request({
    url: '/api/v1/captcha/image',
    method: 'get',
    params: {
      times
    }
  })
}

// 修改密码
export function updatePassword(data) {
  return request({
    url: '/api/v1/sys/account/updatePassWord',
    method: 'post',
    data
  })
}

// //////////// 忘记密码

// 获取验证码
export function getTelCode(tel) {
  return request({
    url: `/api/v1/web/account/send?tel=${tel}`,
    method: 'get'
  })
}
// 验证验证码
export function checkCode({ tel, code }) {
  return request({
    url: `/api/v1/web/account/checkCode?tel=${tel}&code=${code}`,
    method: 'get'
  })
}
// 重置密码（忘记密码）
export function restPassWord(data) {
  return request({
    url: `/api/v1/web/account/restPassWord`,
    method: 'post',
    data
  })
}

// 获取网页信息
export function getWebInfo() {
  return request({
    url: '/api/v1/dts/web-info',
    method: 'get'
  })
}

// 报错网页信息
export function updateWebInfo(data) {
  return request({
    url: '/api/v1/dts/web-info/update',
    method: 'put',
    data
  })
}
