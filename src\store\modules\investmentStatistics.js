const state = {
  // 显示详情信息true 显示列表false
  showDetail: false,
  // 详情时显示接洽记录为true
  showRecords: true,
  // 详情时编辑为false，查看为true
  isLookOver: true,
  // 企业详情
  enterpriseDetails: {}
}

const mutations = {
  setShowDetail: (state, value) => {
    state.showDetail = value
  },
  setShowRecords: (state, value) => {
    state.showRecords = value
  },
  setIsLookOver: (state, value) => {
    state.isLookOver = value
  },
  setEnterpriseDetails: (state, value) => {
    state.enterpriseDetails = value
  }
}

const actions = {
  // setShowDetail({ commit }, value) {
  //   commit('SET_SHOW_DETAIL', value)
  // },
  // setShowRecords({ commit }, value) {
  //   commit('SET_SHOW_RECORDS', value)
  // }
}

export default {
  namespaced: true,
  state,
  mutations,
  actions
}
