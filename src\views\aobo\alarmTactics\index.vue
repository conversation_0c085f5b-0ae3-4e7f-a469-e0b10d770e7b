<template>
  <div class="main-content">
    <div class="top">
      <img v-if="isRecord" src="@/assets/device_manage/<EMAIL>" @click="back" />
      <div v-for="(item, index) in titleList" :key="index" :class="index === titleList.length - 1 ? 'ch_name' : 'pa_name'">
        <span v-if="index !== 0">/</span>
        <span>{{ item }}</span>
      </div>
    </div>
    <notificationRecord v-if="isRecord" />
    <template v-else>
      <el-tabs v-model="tabName">
        <el-tab-pane v-for="item in tabList" :key="item.name" :label="item.label" :name="item.name" />
      </el-tabs>
      <component :is="currentTab.component" @showRecords="showRecords" />
    </template>
  </div>
</template>

<script>
import baseConfig from './modules/baseConfig'
import cloudPhone from './modules/cloudPhone'
import notificationRecord from './modules/notificationRecord'

export default {
  name: 'AlarmTactics',
  components: {
    baseConfig,
    cloudPhone,
    notificationRecord,
  },
  data() {
    return {
      titleList: ['系统管理', '告警策略'],
      tabName: '1',
      isRecord: false,
      tabList: [
        {
          label: '基础配置',
          name: '1',
          component: 'baseConfig',
        },
        {
          label: '云电话配置',
          name: '2',
          component: 'cloudPhone',
        },
      ],
    }
  },
  computed: {
    currentTab() {
      return this.tabList.find((item) => item.name === this.tabName)
    },
  },
  watch: {
    tabName: {
      handler() {
        this.titleList = ['系统管理', '告警策略', this.currentTab.label]
      },
      immediate: true,
    },
  },
  methods: {
    showRecords() {
      this.titleList.push('通知记录')
      this.isRecord = true
    },
    back() {
      this.isRecord = false
      this.titleList.pop()
    },
  },
}
</script>

<style lang="scss" scoped>
.main-content {
  height: calc(100%);
  padding: 30px 0;
  box-sizing: border-box;
  font-family: PingFang SC RE;
  .top {
    display: flex;
    //margin-bottom: 20px;
    align-items: center;
    font-size: 17px;
    font-weight: bold;
    img {
      width: 28px;
      height: 28px;
      cursor: pointer;
      margin-right: 10px;
    }

    .pa_name {
      color: #8d95a5;
      margin-right: 5px;
    }
    .ch_name {
      color: #202225;
      margin-right: 5px;
    }
  }
}
</style>
