<template>
  <div class="main-content">
    <div class="top">
      <div class="left">
        <div class="pa_name">系统管理</div>
        <div class="ch_name">/</div>
        <div class="ch_name">系统日志</div>
      </div>
    </div>
    <div class="line" />
    <div
      class="content-top"
    >
      <div class="search">
        <el-form
          style="display:flex"
          :model="formSearch"
        >
          <el-form-item
            style="margin-right:15px"
            label=""
          >
            <el-input
              v-model="formSearch.keywords"
              suffix-icon="el-icon-search"
              placeholder="请输入用户名/账号/手机号"
              style="width:300px;"
              clearable
              @change="pageNum=1,getList(1)"
            />
          </el-form-item>
          <!--          <el-form-item-->
          <!--            style="margin-right:15px"-->
          <!--            label=""-->
          <!--          >-->
          <!--            <el-select-->
          <!--              v-if="showAdvancedSearch"-->
          <!--              v-model="formSearch.roleIds"-->
          <!--              placeholder="请选择角色名称"-->
          <!--              style="width:220px;"-->
          <!--              clearable-->
          <!--              multiple-->
          <!--              collapse-tags-->
          <!--              @change="pageNum=1,getList(1)"-->
          <!--            >-->
          <!--              <el-option-->
          <!--                v-for="(i,v) in roleData"-->
          <!--                :key="v"-->
          <!--                :label="i.name"-->
          <!--                :value="i.id"-->
          <!--              />-->
          <!--            </el-select>-->
          <!--          </el-form-item>-->
          <el-form-item
            style="margin-right:15px"
            label=""
          >
            <el-select
              v-if="showAdvancedSearch"
              v-model="formSearch.loginPort"
              placeholder="请选择端口"
              style="width:220px;"
              clearable
              multiple
              collapse-tags
              @change="pageNum=1,getList(1)"
            >
              <el-option
                v-for="(i) in portData"
                :key="i"
                :label="i"
                :value="i"
              />
            </el-select>
          </el-form-item>
          <el-form-item
            style="margin-right:15px"
            label=""
          >
            <el-select
              v-if="showAdvancedSearch"
              v-model="formSearch.loginType"
              placeholder="请选择类型"
              style="width:220px;"
              clearable
              @change="pageNum=1,getList(1)"
            >
              <el-option
                v-for="(i,v) in typeList"
                :key="v"
                :label="i.label"
                :value="i.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item
            style="margin-right:15px"
            label=""
          >
            <el-date-picker
              v-if="showAdvancedSearch"
              v-model="formSearch.date"
              type="daterange"
              value-format="yyyy-MM-dd"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              style="width: 260px"
              @change="getListDate"
            />
          </el-form-item>
        </el-form>
      </div>
    </div>
    <div class="content-body">
      <div
        class="top_btn"
      >

        <!--        <commonBtn-->
        <!--          :btn-arr="btnArrTop"-->
        <!--          :operation-list="operationList"-->
        <!--          @click="handleClick"-->
        <!--        />-->
      </div>
      <div class="content-table">
        <el-table
          ref="multipleTable"
          v-loading="loading"
          :header-cell-style="tableHeaderStyle"
          header-row-class-name="table-header"
          height="calc(100vh - 10rem)"
          :data="tableData"
          stripe
          style="width: 100%"
        >

          <el-table-column
            label="序号"
            type="index"
            width="100"
            align="center"
          />
          <el-table-column
            prop="nickName"
            label="用户名"
            show-overflow-tooltip
            align="center"
          >
            <template slot-scope="scope">
              <span>{{ scope.row.nickName || '--' }}</span>
            </template>
          </el-table-column>
          <el-table-column
            prop="username"
            label="账号"
            align="center"
            show-overflow-tooltip
          >
            <template slot-scope="scope">
              <span>{{ scope.row.username || '--' }}</span>
            </template>
          </el-table-column>

          <el-table-column
            prop="phone"
            label="手机号"
            align="center"
          >
            <template slot-scope="scope">
              <span>{{ scope.row.phone || '--' }}</span>
            </template>
          </el-table-column>
          <el-table-column
            prop="roleName"
            label="角色名称"
            align="center"
          >
            <template slot-scope="scope">
              <span> {{
                scope.row.roleName && scope.row.roleName.length
                  ? scope.row.roleName.map(item => item).join('、') : '--'
              }}</span>
            </template>
          </el-table-column>
          <el-table-column
            prop="loginTime"
            label="记录时间"
            align="center"
          >
            <template slot-scope="scope">
              <span>
                {{ scope.row.loginTime || "--" }}
              </span>
            </template>
          </el-table-column>
          <el-table-column
            prop="loginPort"
            label="端口"
            align="center"
          />
          <el-table-column
            prop="loginType"
            label="类型"
            show-overflow-tooltip
            align="center"
          >
            <template slot-scope="scope">
              <span>{{ scope.row.loginType ? scope.row.loginType : '--' }}</span>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <el-pagination
        background
        :current-page.sync="pageNum"
        :page-size="pageSize"
        layout="total,prev, pager, next,sizes, jumper"
        :page-sizes="[10, 20, 50, 100]"
        :total="total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>

    <!-- 删除 -->
    <el-dialog
      title="提示"
      :visible.sync="dialogVisible1"
      width="550px"
      @close="closeDel()"
    >
      <div style="width:100%;padding: 10px 20px;font-size:16px">
        您确定要删除
        <span style="color:#0061CE;font-weight:bold">{{ selectRow.massifName }}</span>
        吗?
      </div>
      <div
        slot="footer"
        class="dialog-footer"
      >
        <el-button @click="closeDel()">取 消</el-button>
        <el-button
          type="primary"
          @click="handleDel()"
        >确 定
        </el-button>
      </div>
    </el-dialog>

  </div>
</template>

<script>
import { mapGetters } from 'vuex'

import { exportLogIn, portDropDownBox } from '@/api/aobo/logManagement/logIn'
import { deepClone, utf8to16 } from '@/utils'
import { page } from '@/api/logs'

export default {
  name: 'PlateData',
  data() {
    return {
      btnArrTop: [
        {
          type: 'top',
          name: '导出',
          tagName: 'export',
          typeName: 'success'
        }
      ],
      btnArrList: [
        {
          type: 'list',
          name: '重命名',
          tagName: 'reName',
          color: '#1071E2'
        },
        {
          type: 'list',
          name: '下载',
          tagName: 'download',
          color: '#1071E2'
        },
        {
          type: 'list',
          name: '权限管理',
          tagName: 'authorityManage',
          color: '#1071E2'
        },
        {
          type: 'list',
          name: '删除',
          tagName: 'del',
          color: '#1071E2'
        }
      ],
      operationList: [],
      formSearch: {
        keywords: '',
        loginType: '',
        loginPort: '',
        startTime: '',
        endTime: '',
        roleIds: [],
        date: []
      },
      showAdvancedSearch: true,
      // 0:登出1:登录  类型
      typeList: [{
        value: 0,
        label: '登出'
      }, {
        value: 1,
        label: '登录'
      }],
      roleData: [], // 角色下拉列表
      portData: [], // 端口下拉列表

      selectList: [], // 选中列表
      loading: false,
      tableData: [],
      pageNum: 1,
      pageSize: 10,
      total: 0,
      selectRow: {},

      dialogVisible: false,
      dialogVisible1: false,
      isAdd: true,
      detailData: {},
      activeName: 'first',
      enterpriseProjectsList: []
    }
  },
  computed: {
    ...mapGetters([
      'tableHeaderStyle', 'btnAuthority'
    ])
  },
  // 监听路由获取当前页面按钮权限
  watch: {
    $route: {
      handler(val, oldVal) {
        const btnAuthority = [...this.btnAuthority]
        const code = val.meta.code || ''
        const objArray = btnAuthority.filter((x) => x.code === code) || []
        const obj = objArray ? objArray[0] : {}
        this.operationList = obj.functionPermissionCode || []
        console.log(code, obj)
      },
      // 深度观察监听
      deep: true,
      immediate: true
    }
  },
  mounted() {
    this.portDropDownBox()
    this.getList(1)
  },
  methods: {
    portDropDownBox() {
      portDropDownBox().then((res) => {
        this.portData = res.data
      })
    },
    handleClickTab(tab, event) {
      console.log(tab, event)
      this.activeName = tab.name
    },
    // 时间改变
    getListDate(val) {
      if (val === null) {
        this.formSearch.date = []
      }
      this.pageNum = 1
      console.log(val, this.formSearch.date)
      this.getList(1)
    },
    getList(isPage) {
      const data = {
        pageNum: isPage ? 1 : this.pageNum,
        pageSize: this.pageSize,
        keyword: this.formSearch.keywords,
        query: {
          loginType: this.formSearch.loginType,
          loginPort: this.formSearch.loginPort[0] || null,
          startTime: this.formSearch.date ? this.formSearch.date[0] : null,
          endTime: this.formSearch.date ? this.formSearch.date[1] : null,
          roleIds: this.formSearch.roleIds[0] || null
        }
      }
      this.loading = true
      page(data).then((res) => {
        this.tableData = res.data.list
        this.total = parseInt(res.data.total, 0)

        this.loading = false
      })
    },
    // 分页
    handleSizeChange(val) {
      console.log(`每页 ${val} 条`)
      this.pageSize = val
      this.getList()
    },
    handleCurrentChange(val) {
      this.pageNum = val
      this.getList()
      console.log(this.pageNum)
    },
    // 多选改变
    handleSelectionChange(list) {
      this.selectList = deepClone(list)
    },

    // 编辑
    handleEdit(row) {
      this.isAdd = false
      this.select = deepClone(row)
      this.detailData = deepClone(row)
      this.dialogVisible = true
    },
    // 删除
    del(row) {
      this.select = JSON.parse(JSON.stringify(row))
      this.dialogVisible1 = true
    },
    // 取消删除
    closeDel() {
      this.dialogVisible1 = false
      this.select = {}
    },

    // 按钮点击事件
    handleClick(tagName, e) {
      switch (tagName) {
        case 'ADD':
          this.isCheck = false
          this.isEdit = false
          this.dialogVisible = true
          break
        case 'IMPORT':
          this.upLoad(e)
          break
        case 'DownloadTemplate':
          this.downloadTemplate()
          break
        case 'export':
          if (this.tableData.length < 1) {
            this.$message.warning('暂无可导出数据')
            return
          }
          this.downLoad()
          break
        case 'UPDATE':
          this.goEdit(e)
          break
        case 'CHECK':
          this.goCheck(e)
          break
        case 'DEL':
          this.handleRemove(e)
          break
        case 'BDEL':
          if (this.deleteList.length === 0) {
            this.$message.warning('至少选择一条数据')
          } else {
            this.remove()
          }
          break
        default:
          break
      }
    },
    // 导出
    downLoad() {
      const data = {
        keyword: this.formSearch.keywords,
        loginType: this.formSearch.loginType,
        loginPort: this.formSearch.loginPort,
        startTime: (this.formSearch.date && this.formSearch.date.length > 0) ? this.formSearch.date[0] : '',
        endTime: (this.formSearch.date && this.formSearch.date.length > 0) ? this.formSearch.date[1] : '',
        roleIds: this.formSearch.roleIds
        // roleIds: ''
      }
      exportLogIn(data).then((res) => {
        const url = window.URL.createObjectURL(
          new Blob([res.data], {
            type: 'application/vnd.ms-excel;charset=UTF-8'
          })
        )
        const temp = res.headers['content-disposition']
          .split(';')[1]
          .split('filename=')[1]
        const index = temp.indexOf('.')
        const str = temp.substr(0, index)
        const fileName = `${utf8to16(unescape(str))}.xlsx`
        const link = document.createElement('a')
        link.href = url
        link.download = fileName
        link.click()
        this.$message({
          type: 'success',
          message: '下载成功'
        })
        console.log('xiazai')
      }).finally(() => {
        // this.exportLoading = false
      })
    }

  }

}
</script>

<style lang="scss" scoped>

.main-content {
  padding-top: 30px;
  .top {
    display: flex;
    margin-bottom: 20px;
    .left {
      display: flex;
      align-items: center;
      font-size: 17px;
      font-weight: bold;
      .pa_name {
        color: #8D95A5;
        margin-right: 5px;
      }
      .ch_name {
        color: #202225;
        margin-right: 5px;
      }
    }
  }

  .content-top {
    //padding-top: 20px;
    display: flex;
    justify-content: space-between;
    border-bottom: 1px solid #F0F0F0;
    // margin-bottom: 20px;

    .search {
      display: flex;
    }
  }

  .content-body {
    // height: calc(100% - 80px);

    .top_btn {
      width: 100%;
      display: flex;
      justify-content: flex-end;
      margin-bottom: 20px;

      .path {
        width: 100%;

        img {
          width: 15px;
          height: 15px;
          margin-right: 5px;
        }

        span {
          font-family: Source Han Sans CN RE;
          font-size: 14px;
        }
      }
    }

    .content-table {
      // overflow: auto;

      .el-table__body-wrapper {
        //height: calc(100% - 120px) !important;
        //overflow: auto;
      }
    }
  }
}

.dialog_footer {
  text-align: center;
  margin-top: 20px;
}
</style>

