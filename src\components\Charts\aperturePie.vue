<template>
  <div
    :id="id"
    :style="{ height: height, width: width }"
  />
</template>

<script>
import elementResizeDetectorMaker from 'element-resize-detector'
import chartMixins from './mixins/index'

export default {
  name: 'AperturePie',
  mixins: [chartMixins],
  props: {
    id: {
      require: true,
      type: String,
      default: 'charts'
    },
    width: {
      require: false,
      type: String,
      default: '100%'
    },
    height: {
      require: false,
      type: String,
      default: '100%'
    },
    propData: {
      require: false,
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      color1: '',
      color2: ''
    }
  },
  watch: {
  },
  mounted() {
    this.$nextTick(() => {
      this.initChart()
    })
    const erd = elementResizeDetectorMaker()
    erd.listenTo(document.getElementById(this.id), (element) => {
      const width = element.offsetWidth
      const height = element.offsetHeight
      this.$nextTick(() => {
        console.log(`Size: ${width}x${height}`)
        // 使echarts尺寸重置
        this.chart = this.$echarts.init(document.getElementById(this.id)).resize()
      })
    })
  },
  methods: {
    initChart() {
      this.chart = this.$echarts.init(document.getElementById(this.id))
      console.log(this.propData)
      if (this.propData.id === 'dashboard0') {
        this.color1 = 'rgba(4, 100, 207, 1)'
        this.color2 = 'rgba(93, 160, 236, 1)'
      } else if (this.propData.id === 'dashboard1') {
        this.color1 = 'rgba(25, 192, 203, 1)'
        this.color2 = 'rgba(103, 221, 225, 1)'
      } else if (this.propData.id === 'dashboard2') {
        this.color1 = 'rgba(255, 162, 16, 1)'
        this.color2 = 'rgba(255, 208, 133, 1)'
      } else if (this.propData.id === 'dashboard3') {
        this.color1 = 'rgba(32, 157, 245, 1)'
        this.color2 = 'rgba(146, 207, 250, 1)'
      }
      this.option = {
        // backgroundColor: '#111',
        title: [{
          text: '主导产业',
          x: 'center',
          top: '52%',
          textStyle: {
            color: this.propData.color,
            fontSize: 11,
            fontWeight: '100'
          }
        }, { // 数量
          text: this.propData.value,
          x: 'center',
          top: '38%',
          textStyle: {
            fontSize: '17',
            color: 'rgba(80, 80, 80, .8)',
            fontFamily: 'DIN',
            foontWeight: '500'
          }
        }],
        polar: { // 中间饼图位置
          radius: ['53%', '62%'],
          center: ['50%', '50%']
        },
        angleAxis: {
          max: 5500,
          show: false
        },
        radiusAxis: {
          type: 'category',
          show: true,
          axisLabel: {
            show: false
          },
          axisLine: {
            show: false

          },
          axisTick: {
            show: false
          }
        },
        series: [
          {
            name: '',
            type: 'bar',
            roundCap: true,
            barWidth: 60,
            showBackground: true,
            backgroundStyle: {
              color: 'rgba(224, 224, 224, 1)'
            },
            data: [this.propData.value],
            coordinateSystem: 'polar',
            itemStyle: {
              normal: {
                color: new this.$echarts.graphic.LinearGradient(0, 1, 0, 0, [{ // 完成得圆环颜色
                  offset: 0,
                  color: this.color1
                }, {
                  offset: 1,
                  color: this.color2
                }])
              }
            }

          },
          {
            name: '',
            type: 'pie',
            startAngle: 90,
            radius: ['70%', '76%'],
            hoverAnimation: false,
            center: ['50%', '50%'],
            itemStyle: {
              normal: {
                labelLine: {
                  show: false
                },
                color: 'rgba(93, 160, 236, .1)',
                shadowBlur: 10,
                shadowColor: 'rgba(93, 160, 236, .1)'
              }
            },
            data: [{
              value: 100
            }]
          }
        ]
      }
      this.chart.setOption(this.option, true)
    }
  }
}
</script>

<style>

</style>

