import request from '@/utils/request'

// 获取项目数
export function getProjectDetails() {
    return request({
        url:'/api/v1/web/home/<USER>',
        method: 'get'
    })
}

// 获取设备数
export function getDeviceDetails() {
    return request({
        url:'/api/v1/web/home/<USER>',
        method: 'get'
    })
}

// 获取异常分析分页
export function getAnalysPage(data) {
    return request({
        url:'/api/v1/web/home/<USER>',
        method:'post',
        data
    })
}

// 获取异常事件
export function getEventPage(data) {
    return request({
        url:'/api/v1/web/home/<USER>',
        method:'post',
        data
    })
}
// 获取周传输个数
export function getWeekFile() {
    return request({
        url:'/api/v1/web/home/<USER>',
        method:'get'
    })
}
// 获取传输趋势
export function getTransmissionTrend(data) {
    return request({
        url:'/api/v1/web/home/<USER>',
        method:'post',
        data
    })
}
// 获取数据中心容量
export function getDataCenters() {
    return request({
        url:'/api/v1/web/home/<USER>',
        method:'get'
    })
}
// 项目排名
export function getProjectRankings(data) {
    return request({
        url:'/api/v1/web/home/<USER>',
        method:'post',
        data
    })
}
// 获取单位
export function getUnit() {
    return request({
        url:'/api/v1/web/home/<USER>',
        method:'get'
    })
}