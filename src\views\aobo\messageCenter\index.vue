<template>
  <div class="main-content">
    <div class="top">
      <div class="left">
        <div class="pa_name">系统管理</div>
        <div class="ch_name">/</div>
        <div class="ch_name">消息中心</div>
      </div>
    </div>
    <div class="line" />
    <div class="content-body">
      <div
        class="top_btn"
      >
        <!-- <el-button
          type="primary"
          @click="batchRead"
        >一键已读</el-button> -->
      </div>
      <div class="content-table">
        <el-table
          ref="multipleTable"
          v-loading="loading"
          :header-cell-style="tableHeaderStyle"
          header-row-class-name="table-header"
          height="67vh"
          :data="tableData"
          stripe
          style="width: 100%"
        >
          <el-table-column
            label="序号"
            type="index"
            width="100"
            align="center"
          />
          <el-table-column
            prop="status"
            label="状态"
            show-overflow-tooltip
            align="center"
          >
            <template slot-scope="scope">
              <span>{{ scope.row.status === 0 ? '未读' : '已读' }}</span>
            </template>
          </el-table-column>
          <el-table-column
            prop="msgContent"
            label="消息内容"
            show-overflow-tooltip
            align="center"
          >
            <template slot-scope="scope">
              <span>{{ scope.row.msgContent || '--' }}</span>
            </template>
          </el-table-column>
          <el-table-column
            prop="createTime"
            label="创建时间"
            align="center"
          >
            <template slot-scope="scope">
              <span>
                {{ scope.row.createTime || "--" }}
              </span>
            </template>
          </el-table-column>
          <el-table-column
            fixed="right"
            label="操作"
            width="250"
          >
            <template slot-scope="scope">
              <el-button
                type="text"
                size="small"
                @click="handleClick(scope.row)"
              >查看</el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <el-pagination
        background
        :current-page.sync="pageNum"
        :page-size="pageSize"
        layout="total, prev, pager, next, sizes, jumper"
        :page-sizes="[10, 20, 50, 100]"
        :total="total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>

  </div>
</template>

<script>
import { mapGetters } from 'vuex'

import { page, checkMsg } from '@/api/messageCenter'

export default {
  name: 'PlateData',
  data() {
    return {
      menuList: JSON.parse(localStorage.getItem('menuList')),
      btnArrTop: [
        {
          type: 'top',
          name: '导出',
          tagName: 'export',
          typeName: 'success'
        }
      ],
      btnArrList: [
        {
          type: 'list',
          name: '重命名',
          tagName: 'reName',
          color: '#1071E2'
        },
        {
          type: 'list',
          name: '下载',
          tagName: 'download',
          color: '#1071E2'
        },
        {
          type: 'list',
          name: '权限管理',
          tagName: 'authorityManage',
          color: '#1071E2'
        },
        {
          type: 'list',
          name: '删除',
          tagName: 'del',
          color: '#1071E2'
        }
      ],
      operationList: [],
      formSearch: {
        keywords: '',
        loginType: '',
        loginPort: '',
        startTime: '',
        endTime: '',
        roleIds: [],
        date: []
      },
      showAdvancedSearch: true,
      // 0:登出1:登录  类型
      typeList: [{
        value: 0,
        label: '登出'
      }, {
        value: 1,
        label: '登录'
      }],
      roleData: [], // 角色下拉列表
      portData: [], // 端口下拉列表

      selectList: [], // 选中列表
      loading: false,
      tableData: [],
      pageNum: 1,
      pageSize: 10,
      total: 0,
      selectRow: {},

      dialogVisible: false,
      dialogVisible1: false,
      isAdd: true,
      detailData: {},
      activeName: 'first',
      enterpriseProjectsList: []
    }
  },
  computed: {
    ...mapGetters([
      'tableHeaderStyle', 'btnAuthority'
    ])
  },
  // 监听路由获取当前页面按钮权限
  watch: {
    $route: {
      handler(val, oldVal) {
        const btnAuthority = [...this.btnAuthority]
        const code = val.meta.code || ''
        const objArray = btnAuthority.filter((x) => x.code === code) || []
        const obj = objArray ? objArray[0] : {}
        this.operationList = obj.functionPermissionCode || []
        console.log(code, obj)
      },
      // 深度观察监听
      deep: true,
      immediate: true
    }
  },
  mounted() {
    this.getList(1)
  },
  methods: {
    handleClickTab(tab, event) {
      console.log(tab, event)
      this.activeName = tab.name
    },

    getList(isPage) {
      const data = {
        pageNum: isPage ? 1 : this.pageNum,
        pageSize: this.pageSize,
        query: {
        }
      }
      this.loading = true
      page(data).then((res) => {
        this.tableData = res.data.records
        this.total = parseInt(res.data.total, 0)

        this.loading = false
      }).catch((res) => {
        this.loading = false
      })
    },
    // 分页
    handleSizeChange(val) {
      console.log(`每页 ${val} 条`)
      this.pageSize = val
      this.getList()
    },
    handleCurrentChange(val) {
      this.pageNum = val
      this.getList()
      console.log(this.pageNum)
    },

    // 按钮点击事件
    handleClick(data) {
      if (!this.menuList.includes('BJXX')) {
        this.$message.warning('暂未配置报警信息权限，请联系管理员！')
        return
      }
      checkMsg({
        id: data.id
      }).then((res) => {
        this.$router.push({
          path: '/alarmInfo/alarmInfo'
        })
      })
    },
    // 一键已读
    batchRead() {
      checkMsg().then(() => {
        this.pageNum = 1
        this.getList(1)
      })
    }
  }

}
</script>

<style lang="scss" scoped>
.main-content {
  padding-top: 30px;
  font-family: PingFang SC RE;
  .top {
    display: flex;
    margin-bottom: 20px;
    .left {
      display: flex;
      align-items: center;
      font-size: 17px;
      font-weight: bold;
      .pa_name {
        color: #8D95A5;
        margin-right: 5px;
      }
      .ch_name {
        color: #202225;
        margin-right: 5px;
      }
    }
  }
  .line {
    width: 100%;
    height: 1px;
    background: #E5E5E5;
    margin-bottom: 20px;
  }
  .content-top {
    padding-top: 20px;
    display: flex;
    justify-content: space-between;
    border-bottom: 1px solid #F0F0F0;
    margin-bottom: 20px;

    .search {
      display: flex;
    }
  }

  .content-body {
    height: calc(100% - 80px);

    .top_btn {
      width: 100%;
      display: flex;
      justify-content: flex-end;
      margin-bottom: 20px;

      .path {
        width: 100%;

        img {
          width: 15px;
          height: 15px;
          margin-right: 5px;
        }

        span {
          font-family: Source Han Sans CN RE;
          font-size: 14px;
        }
      }
    }

    .content-table {
      margin-bottom: 30px;
      // overflow: auto;

      .el-table__body-wrapper {
        //height: calc(100% - 120px) !important;
        //overflow: auto;
      }
    }
  }
}

.dialog_footer {
  text-align: center;
  margin-top: 20px;
}
</style>

