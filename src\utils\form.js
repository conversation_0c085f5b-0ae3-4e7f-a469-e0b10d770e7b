
/**
 * 表单提交方法
 * @param {Object} VueComponent vue组件对象
 * @param {String} formName 需要验证的表单名称
 * @param {function} api 需要调用的api
 * @param {string} msg 需要调用api成功后的提示语（非必填）
 * @param {String} windowName 需要关闭的弹窗（非必填）
 * @param {function} callback api调用成功的回调（非必填）
 * @param {function} catchback api调用失败的回调（非必填）
 * @param {Array} needsVerifiedProp 需要校验的表单（用于非必填项提交）
 */
export function submit(args) {
  const {
    VueComponent, formName, api, windowName, msg,
    buttonLoadingName, callback, catchback, needsVerifiedProp
  } = args
  const submitForm = () => {
    // 按钮loading属性，防止重复多次点击
    if (buttonLoadingName) {
      VueComponent[buttonLoadingName] = true
    } else {
      VueComponent.submitLoading = true
    }
    api().then(() => {
      // 新增、修改成功后调用获取列表接口刷新数据
      if (callback) {
        callback()
      } else {
        VueComponent.getList()
      }
      // 关闭新增、修改弹窗
      if (windowName) {
        VueComponent[windowName] = false
      } else {
        VueComponent.showAddWindow = false
      }
      VueComponent.$message.success(msg || '操作成功')
      // 按钮loading属性，防止重复多次点击
      if (buttonLoadingName) {
        VueComponent[buttonLoadingName] = false
      } else {
        VueComponent.submitLoading = false
      }
    }).catch(() => {
      if (catchback) {
        catchback()
      }
      if (buttonLoadingName) {
        VueComponent[buttonLoadingName] = false
      } else {
        VueComponent.submitLoading = false
      }
    })
  }
  console.log(formName || 'form')

  if (needsVerifiedProp && needsVerifiedProp.length > 0) {
    // 局部表单验证
    let allValid = true
    const validateItem = (item) => {
      VueComponent.$refs[formName || 'form'].validateField(item, (errMsg) => {
        if (errMsg) {
          allValid = false
        }
      })
    }
    for (const item of needsVerifiedProp) {
      validateItem(item)
    }
    if (allValid) {
      submitForm()
    }
  } else {
    // 全局验证表单
    VueComponent.$refs[formName || 'form'].validate((valid) => {
      if (valid) {
        submitForm()
      }
    })
  }
}

/**
 * 确认弹框方法
 * @param {vue对象} VueComponent
 * @param {确认框内容} content
 * @param {确认框标题} title
 * @param {需要调用的后台API} api
 * @param {执行成功后的回调，非必传} callback
 */
export function confirm(args) {
  const {
    VueComponent, content, title, api, callback
  } = args
  VueComponent.$confirm(content || '您确定要删除该项吗？', title || '确认删除', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
    beforeClose: (action, instance, done) => {
      if (action === 'confirm') {
        instance.confirmButtonLoading = true
        instance.confirmButtonText = '执行中...'
        api().then(() => {
          instance.confirmButtonLoading = false
          instance.confirmButtonText = '确定'
          VueComponent.$message.success('删除成功')
          if (callback) {
            callback()
          } else {
            VueComponent.getList()
          }
          done()
        }).catch(() => {
          instance.confirmButtonLoading = false
          instance.confirmButtonText = '确定'
        })
      } else {
        done()
      }
    }
  })
}
