<template>
  <div>
    <div>
      <slot />
      <el-button
        class="text"
        type="text"
        :style="{color: disabled ? '#c0c4cc' : color}"
        :disabled="disabled"
        @click="btnClick(tagName, row)"
      >{{ text }}</el-button>
    </div>
  </div>
</template>

<script>

export default {
  name: 'ListButton',
  components: {},
  props: {
    row: { // 选中行
      type: Object,
      default: () => {}
    },
    text: {
      type: String,
      default: ''
    },
    tagName: {
      type: String,
      default: ''
    },
    color: {
      type: String,
      default: '#1071E2'
    },
    disabled: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {}
  },
  created() {},
  methods: {
    btnClick(tagName, row) {
      this.$emit('click', tagName, row)
    }
  }
}
</script>

<style lang="scss" scoped>
.text {
  font-family: Source Han Sans CN Regular;
  font-size: 13px;
}
</style>
