<template>
  <div
    style="position:relative;"
    :class="iconImage ? 'menu_item' : 'menu_item_child'"
  >
    <img
      v-if="isActive"
      src="@/assets/login/<EMAIL>"
      class="bg_img"
    >
    <img
      v-if="iconImage"
      :src="iconImage"
      class="icon_img"
    >
    <div class="text">{{ title }}</div>
  </div>
</template>

<script>
export default {
  name: 'MenuItem',
  props: {
    icon: {
      type: String,
      default: ''
    },
    iconImage: {
      type: String,
      default: ''
    },
    title: {
      type: String,
      default: ''
    },
    isActive: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {}
  }
  // render(h, context) {
  //   const { icon, iconImage, title } = context.props
  //   const vnodes = []
  //   if (icon) {
  //     if (icon.includes('el-icon')) {
  //       vnodes.push(<i class={[icon, 'sub-el-icon']} />)
  //     } else {
  //       // const iconClass = icon
  //       vnodes.push(<svg-icon icon-class={icon}/>)
  //     }
  //   }
  //   if (iconImage) {
  //     vnodes.push(<el-image class={'img'} src={iconImage}/>)
  //   }
  //   if (title) {
  //     vnodes.push(<span slot='title' class={'text'}>{(title)}</span>)
  //   }
  //   return vnodes
  // }
}
</script>

<style lang="scss" scoped>
.bg_img {
  width: 100%;
  height: 100%;
}
.icon_img {
  position: absolute;
  width: 30px;
  height: 30px;
  top: 15%;
  left: 40%;
}
.text {
  font-family: Source Han Sans CN RE;
  font-size:15px;
  margin-top: -10px;
  position: absolute;
  bottom: 10%;
  right: 0;
  width: 100%;
  text-align: center;
}
.menu_item {
  height:100px;
  width:120px;
}
.menu_item_child {
  height:50px;
  width:120px;
  .text {
    font-size: 14px;
  }
}
</style>
