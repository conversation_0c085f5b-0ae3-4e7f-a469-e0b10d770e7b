import Vue from 'vue'
import Vuex from 'vuex'
import threeJs from '@/store/modules/threeJs'
import getters from './getters'
import app from './modules/app'
import settings from './modules/settings'
// eslint-disable-next-line import/no-cycle
import user from './modules/user'
import style from './modules/style'
import investmentStatistics from './modules/investmentStatistics'
import permission from './modules/permission'

Vue.use(Vuex)

const store = new Vuex.Store({
  modules: {
    app,
    settings,
    user,
    style,
    investmentStatistics,
    permission,
    threeJs

  },
  getters
})

export default store
