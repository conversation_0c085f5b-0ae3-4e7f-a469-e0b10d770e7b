const scriptList = [`https://api.tianditu.gov.cn/api?v=4.0&tk=6065a143e97b5d335526d0d1896bf990`]

export default new Promise((resolve) => {
  const loads = scriptList.map((v, index) => {
    const has = document.getElementById(`bigemap_script_${index}`)
    if (has) return
    const script = document.createElement('script')
    script.type = 'text/javascript'
    script.id = `bigemap_script_${index}`
    script.async = true
    script.src = v
    document.head.appendChild(script)
    // eslint-disable-next-line consistent-return
    return script
  })
  if (loads) {
    console.log('loads', loads)
    const end = loads.pop()
    console.log('end', end)
    end.onload = resolve
  } else {
    resolve()
  }
})
