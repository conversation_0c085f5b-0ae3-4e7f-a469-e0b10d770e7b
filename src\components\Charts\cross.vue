<template>
  <div
    :id="id"
    :style="{ height: height, width: width }"
  />
</template>

<script>
import elementResizeDetectorMaker from 'element-resize-detector'
import chartMixins from './mixins/index'

export default {
  name: 'Cross',
  mixins: [chartMixins],
  props: {
    id: {
      require: true,
      type: String,
      default: 'charts'
    },
    width: {
      require: false,
      type: String,
      default: '100%'
    },
    height: {
      require: false,
      type: String,
      default: '100%'
    },
    propData: {
      require: false,
      type: Object,
      default: () => {}
    }
  },
  watch: {
    propData(newValue) {
      if (newValue) {
        if (this.chart) {
          this.chart.clear()
          this.$nextTick(() => {
            this.initChart()
          })
          console.log(this.propData, newValue)
        }
        this.$nextTick(() => {
          this.initChart()
        })
      }
    },
    deep: true
  },
  mounted() {
    this.$nextTick(() => {
      this.initChart()
    })
    const erd = elementResizeDetectorMaker()
    erd.listenTo(document.getElementById(this.id), (element) => {
      const width = element.offsetWidth
      const height = element.offsetHeight
      this.$nextTick(() => {
        console.log(`Size: ${width}x${height}`)
        // 使echarts尺寸重置
        this.chart = this.$echarts.init(document.getElementById(this.id)).resize()
      })
    })
  },
  methods: {
    initChart() {
      this.chart = this.$echarts.init(document.getElementById(this.id))
      this.option = {
        title: {
          text: this.propData.name,
          left: 0,
          top: 0,
          textStyle: {
            fontSize: 14,
            color: 'rgba(0, 0, 0, 0.647)'
          },
          show: true
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'cross',
            crossStyle: {
              color: '#999'
            }
          }
        },
        legend: {
          data: ['绝对值(亿元)', '增速 （%）'],
          right: '10%'
        },
        xAxis: [
          {
            type: 'category',
            data: ['1-2月', '1-3月', '1-4月', '1-5月', '1-6月', '1-7月', '1-8月', '1-9月', '1-10月', '1-11月', '1-12月'],
            axisPointer: {
              type: 'shadow'
            }
          }
        ],
        grid: {
          top: '20%', // 距上边距

          left: '5%', // 距离左边距

          right: '3%', // 距离右边距

          bottom: '10%' // 距离下边距

        },
        yAxis: [
          {
            type: 'value',
            min: 0,
            max: 2000,
            interval: 250,
            axisLabel: {
              formatter: '{value} '
            },
            splitLine: {
              show: true,
              lineStyle: {
                type: 'dashed'
              }
            }
          },
          {
            type: 'value',
            name: '增速',
            axisLabel: {
              formatter: '{value} '
            },
            splitLine: {
              show: true,
              lineStyle: {
                type: 'dashed'
              }
            }

          }
        ],
        series: [
          {
            name: '绝对值(亿元)',
            type: 'bar',
            data: this.propData.xData,
            itemStyle: {
              normal: {
                color: '#1890FF'
              }
            },
            barWidth: 16
          },
          {
            name: '增速 （%）',
            type: 'line',
            yAxisIndex: 1,
            data: this.propData.yData,
            areaStyle: {
              color: '#309FEA',
              opacity: '0.1',
              origin: 'start'
            },
            symbol: 'none',
            color: ['#13C2C2'], // 折线条的颜色
            smooth: true
          }
        ]
      }
      this.chart.setOption(this.option, true)
    }
  }
}
</script>

<style>

</style>

