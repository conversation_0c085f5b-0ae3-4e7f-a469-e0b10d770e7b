import request from '@/utils/request'

export function doPage(data) {
  return request({
    url: '/api/v1/cloud/cloudTelephony/doPage',
    method: 'post',
    data
  })
}
export function doSave(data) {
  return request({
    url: '/api/v1/cloud/cloudTelephony/doSave',
    method: 'post',
    data
  })
}
export function doUpdate(data) {
  return request({
    url: '/api/v1/cloud/cloudTelephony/doUpdate',
    method: 'post',
    data
  })
}
export function doDelete(id) {
  return request({
    url: `/api/v1/cloud/cloudTelephony/doDelete?id=${id}`,
    method: 'get'
  })
}
export function changeState(id) {
  return request({
    url: `/api/v1/cloud/cloudTelephony/changeState?id=${id}`,
    method: 'get'
  })
}
export function doDropSpace() {
  return request({
    url: '/api/v1/cloud/cloudTelephony/doDropSpace',
    method: 'get'
  })
}
export function doDropPerson() {
  return request({
    url: '/api/v1/sys/account/doDropPerson',
    method: 'get'
  })
}
