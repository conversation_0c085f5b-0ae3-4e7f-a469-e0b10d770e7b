import { debounce } from '@/utils'

export default {
  data() {
    return {
      chartsTimer: null,
      option: {},
      animationTimer: null,
      resizeHandler: null,
      chart: null
    }
  },
  mounted() {
    this.resizeHandler = debounce(() => {
      if (this.chart) {
        this.chart.resize()
      }
    }, 100)
    window.addEventListener('resize', this.resizeHandler)
    this.$nextTick(() => {
      this.initChart()
    })
  },
  before<PERSON><PERSON><PERSON>() {
    if (!this.chart) {
      return
    }
    this.chart.clear()
    this.chart.dispose()
    this.chart = null
    clearInterval(this.animationTimer)
    window.removeEventListener('resize', this.resizeHandler)
  },
  methods: {
    /**
     * echarts 显示加载动画
     */
    showLoading() {
      this.chart.showLoading({
        text: '加载中',
        color: '#595959',
        textColor: '#595959',
        maskColor: '#f6f6f6',
        zlevel: 10
      })
    },
    /**
     * echarts 隐藏加载动画
     */
    hideLoading() {
      this.chart.hideLoading()
    },
    /**
     * echarts 暂无数据占位
     */
    noData() {
      this.chart.showLoading({
        text: '暂无数据',
        color: 'transparent',
        textColor: '#595959',
        maskColor: 'rgba(3,169,255,0)',
        fontSize: '16px',
        zlevel: 10
      })
    },

    /** 图表动画
     * @param {chartDataLength} 数据数组长度
     * @param {curIndex} 当前要展示的数据index
     */
    autoPlayTool(chartDataLength, curIndex = 0, seriesIndex = 0, speed = 2000) {
      clearInterval(this.chartsTimer)
      const setpfun = () => {
        this.chart.dispatchAction({
          type: 'downplay',
          seriesIndex,
          dataIndex: curIndex - 1 < 0 ? chartDataLength - 1 : curIndex - 1
        })
        this.chart.dispatchAction({
          type: 'highlight',
          seriesIndex,
          dataIndex: curIndex
        })
        this.chart.dispatchAction({
          type: 'showTip',
          seriesIndex,
          dataIndex: curIndex
        })
        if (curIndex === chartDataLength - 1) {
          curIndex = 0
        } else {
          curIndex++
        }
      }
      this.chartsTimer = setInterval(setpfun, speed)
      this.chart.on('mouseover', () => {
        clearInterval(this.chartsTimer)
      })
      this.chart.on('click', () => {
      })
      this.chart.on('mouseout', () => {
        clearInterval(this.chartsTimer)
        this.chartsTimer = setInterval(setpfun, speed)
      })
    }
  }
}
