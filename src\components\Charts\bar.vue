<template>
  <div
    :id="id"
    :style="{ height: height, width: width }"
  />
</template>

<script>
import elementResizeDetectorMaker from 'element-resize-detector'
import chartsMixIn from './mixins'

export default {
  name: 'Bar',
  mixins: [chartsMixIn],
  props: {
    id: {
      require: true,
      type: String,
      default: 'charts'
    },
    width: {
      require: false,
      type: String,
      default: '100%'
    },
    height: {
      require: false,
      type: String,
      default: '100%'
    },
    propData: {
      require: false,
      type: Object,
      default: () => { }
    }
  },
  mounted() {
    this.$nextTick(() => {
      this.initChart()
    })
    console.log(this.propData, 66)
    const erd = elementResizeDetectorMaker()
    erd.listenTo(document.getElementById(this.id), (element) => {
      const width = element.offsetWidth
      const height = element.offsetHeight
      this.$nextTick(() => {
        console.log(`Size: ${width}x${height}`)
        // 使echarts尺寸重置
        this.chart = this.$echarts.init(document.getElementById(this.id)).resize()
      })
    })
  },
  methods: {
    initChart() {
      this.chart = this.$echarts.init(document.getElementById(this.id))
      this.option = {
        tooltip: {
          trigger: 'axis',
          axisPointer: { // 坐标轴指示器，坐标轴触发有效
            type: 'line' // 默认为直线，可选为：'line' | 'shadow'
          }
        },
        title: {
          text: '预警信息变化次数',
          left: 20,
          top: 0,
          textStyle: {
            fontSize: 16,
            color: '#505050'
          },
          show: true
        },
        xAxis: {
          type: 'category',
          data: this.propData.xData,
          axisLine: {
            lineStyle: {
              color: '#909090'
            }
          }
        },
        grid: {
          top: '20%', // 距上边距

          left: '5%', // 距离左边距

          right: '5%', // 距离右边距

          bottom: '10%' // 距离下边距

        },
        yAxis: {
          type: 'value',
          axisLine: {
            lineStyle: {
              color: '#909090'
            }
          },
          splitLine: {
            show: true,
            lineStyle: {
              type: 'dashed'
            }
          }
        },
        series: [{
          data: this.propData.yData,
          type: 'bar',
          itemStyle: {
            normal: {
              color: '#4694EC'
            }
          },
          barWidth: 16
        }]
      }
      this.chart.setOption(this.option, true)
    }
  }
}
</script>

<style>

</style>

