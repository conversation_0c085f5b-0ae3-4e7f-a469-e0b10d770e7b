import getPageTitle from '@/utils/get-page-title'
import { getToken } from '@/utils/auth' // get token from cookie
import { Message } from 'element-ui'
import NProgress from 'nprogress' // progress bar
import router, { resetRouter } from './router'
import store from './store'
import 'nprogress/nprogress.css' // progress bar style

NProgress.configure({ showSpinner: false }) // NProgress Configuration

const whiteList = ['/login', '/forgotPassword'] // no redirect whitelist

router.beforeEach(async(to, from, next) => {
  // start progress bar
  NProgress.start()

  // set page title
  document.title = getPageTitle(to.meta.title)

  // determine whether the user has logged in
  const hasToken = getToken()
  if (hasToken) {
    if (to.path === '/login') {
      // if is logged in, redirect to the home page
      next({ path: '/' })
      NProgress.done()
    } else if (store.getters.hasFilterAsyncRoutes) {
      next()
    } else {
      try {
        // const menuList = []
        // const getCodeArr = (data) => {
        //   data.forEach((item) => {
        //     menuList.push(item.menuCode)
        //     if (item.sysMenuList) {
        //       getCodeArr(item.sysMenuList)
        //     }
        //   })
        // }
        // getCodeArr(JSON.parse(localStorage.getItem('menuList')))
        const menuList = JSON.parse(localStorage.getItem('menuList'))
        // 没有菜单时跳转到登录页
        if (!menuList || menuList.length === 0) {
          await store.dispatch('user/resetToken')
          next(`/login?redirect=${to.path}`)
          NProgress.done()
          return
        }

        store.dispatch('permission/generateRoutes', menuList).then((accessRoutes) => {
          router.addRoutes(accessRoutes)
          if (to.fullPath === '/') {
            next({ path: `${accessRoutes[0].path}/${accessRoutes[0].children[0].path}` })
          } else {
            next({ ...to, replace: true })
          }
          NProgress.done()
        })
      } catch (error) {
        // remove token and go to login page to re-login
        await store.dispatch('user/resetToken')
        Message.error(error || 'Has Error')
        next(`/login?redirect=${to.path}`)
        NProgress.done()
      }
    }
  } else {
    if (whiteList.indexOf(to.path) !== -1) {
    // in the free login whitelist, go directly
      next()
      NProgress.done()
    } else {
      store.commit('permission/HAS_FILTER_ROUTES', false)
      resetRouter()
      next(`/login?redirect=${to.path}`)
      NProgress.done()
    }
    return true
  }
  router.afterEach(() => {
  // finish progress bar
    NProgress.done()
  })
  return false
})
