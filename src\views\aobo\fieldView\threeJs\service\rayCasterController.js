import { Raycaster, Vector2 } from 'three'
import { canvasSize } from '@/views/aobo/fieldView/threeJs/service/core'

export class RayCasterController {
   camera

   hoverPoint = new Vector2(0, 0)

  tooltipRaycaster = new Raycaster()

  raycastObjects = []

   onMouseup

   onMousedown

   constructor(camera) {
     this.camera = camera
     this.tooltipRaycaster.far = 15
   }

  bindClickRayCastObj = (raycastObjects = [], onClick) => {
    let downX = 0
    let downY = 0
    this.onMousedown = (e) => {
      downX = e.screenX
      downY = e.screenY
    }
    window.addEventListener('mousedown', this.onMousedown)
    this.raycastObjects = raycastObjects
    const clickRaycaster = new Raycaster()
    this.onMouseup = (e) => {
      if (e.button !== 0) return
      const offsetX = Math.abs(e.screenX - downX)
      const offsetY = Math.abs(e.screenY - downY)
      if (offsetX > 1 || offsetY > 1) return
      const position = new Vector2((e.offsetX / canvasSize.width) * 2 - 1, 1 - (e.offsetY / canvasSize.height) * 2)
      clickRaycaster.setFromCamera(position, this.camera)
      const intersects = clickRaycaster.intersectObjects(raycastObjects)
      if (intersects.length) {
        onClick(intersects[0].object)
      }
    }
    window.addEventListener('mouseup', this.onMouseup)
  }

  updateTooltipRayCast(onShow, onHide) {
    if (this.raycastObjects.length) {
      this.tooltipRaycaster.setFromCamera(this.hoverPoint, this.camera)
      const intersects = this.tooltipRaycaster.intersectObjects(this.raycastObjects)
      if (intersects.length && intersects[0].object.userData.title) {
        onShow(intersects[0].object)
      } else {
        onHide()
      }
    }
  }

  restart() {
    window.addEventListener('mousedown', this.onMousedown)
    window.addEventListener('mouseup', this.onMouseup)
  }

  dispose() {
    window.removeEventListener('mousedown', this.onMousedown)
    window.removeEventListener('mouseup', this.onMouseup)
  }
}
