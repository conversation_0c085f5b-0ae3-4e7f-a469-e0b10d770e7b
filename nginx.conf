daemon off;
worker_processes auto;

events {
    worker_connections 1024;
}

http {
    include       mime.types;
    default_type  application/html;
    log_format  main  '$remote_addr - $remote_user [$time_local] "$request" '
                      '$status $body_bytes_sent "$http_referer" '
                      '"$http_user_agent" "$http_x_forwarded_for"';
    access_log  /var/log/nginx/access.log  main;
    error_log  /var/log/nginx/error.log  error;
    sendfile        on;
    keepalive_timeout  65;
    rewrite_log on;
    client_max_body_size 0;

    server {
        listen 80 default_server;
        root /usr/share/nginx/html;

        location /api {
            proxy_set_header Host $host:$server_port;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_pass http://**********:38080/api;
        }
    }
}
