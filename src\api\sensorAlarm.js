import request from '@/utils/request'

// 查询报警分页
export function doPage(data) {
  return request({
    url: '/api/v1/move-sensor/alarm/page',
    method: 'post',
    data
  })
}
// 批量处理告警
export function disposeAlarm(data) {
  return request({
    url: '/api/v1/move-sensor/alarm/dispose',
    method: 'post',
    data
  })
}

// 批量导出
export function batchExport(data) {
  return request({
    url: '/api/v1/move-sensor/alarm/export',
    method: 'post',
    data,
    responseType: 'blob'
  })
}

// 一键处置所有报警
export function disposeAllAlarm(data) {
  return request({
    url: '/api/v1/move-sensor/alarm/dispose-all',
    method: 'post',
    data
  })
}

// 获取报警树(至多到工作面)
export function getAlarmTree() {
  return request({
    url: '/api/v1/dts/alarm/alarmTree',
    method: 'get'
  })
}

// 批量删除
export function del(data) {
  return request({
    url: '/api/v1/move-sensor/alarm',
    method: 'delete',
    data
  })
}
