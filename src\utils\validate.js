/* eslint-disable max-len */
/**
 * Created by PanJiaChen on 16/11/18.
 */

/**
 * @param {string} path
 * @returns {Boolean}
 */
export function isExternal(path) {
  return /^(https?:|mailto:|tel:)/.test(path)
}

/**
 * @param {string} str
 * @returns {Boolean}
 */
export function validUsername(str) {
  const valid_map = ['admin', 'editor']
  return valid_map.indexOf(str.trim()) >= 0
}

/**
 * 验证数字或1-4位小数
 * @param {*} rule
 * @param {*} value
 * @param {*} callback
 * @returns
 */
export function validateAmount(rule, value, callback) {
  if (!/^\d+(\.\d{1,4})?$/.test(value)) {
    callback(new Error('请输入数字或1-4位小数'))
  } else {
    callback()
  }
}

/**
 * 验证手机或座机号码
 * @param {*} rule
 * @param {*} value
 * @param {*} callback
 * @returns
 */
export function validatePhoneNumber(rule, value, callback) {
  if (value) {
    if (!/^((0\d{2,3}-?\d{7,8})|(1[3465789]\d{9}))$/.test(value)) {
      callback(new Error('请输入正确的号码'))
    } else {
      callback()
    }
  } else {
    callback(new Error('请输入电话号码'))
  }
}
/**
+ * 验证数值允许输入负数,最多两位小数
+ * @param {*} rule
+ * @param {*} value
+ * @param {*} callback
+ * @returns
+ */
export function validateNum(rule, value, callback) {
  if (value) {
    // if (!/(^-?(?:\d+|\d{1,3}(?:,\d{3})+)(?:\.\d{1,2})?$)/.test(value)) { //可输入正数，负数，最多两位小数
    if (!/^[-+]?[0-9]+(\.[0-9]+)?$/.test(value)) {
      callback(new Error('请输入数字，可输入负数、小数'))
    } else {
      callback()
    }
  } else {
    callback()
  }
}
/**
 * 统一社会信用代码
 * @param {*} rule
 * @param {*} value
 * @param {*} callback
 * @returns
 */
export function validateSocialCreditCode(rule, value, callback) {
  if (value) {
    if (!/^([0-9A-HJ-NPQRTUWXY]{2}\d{6}[0-9A-HJ-NPQRTUWXY]{10}|[1-9]\d{14})$/.test(value)) {
      callback(new Error('统一社会信用代码格式不正确'))
    } else {
      callback()
    }
  } else {
    callback(new Error('请输入统一社会信用代码'))
  }
}
/**
 * 组织机构代码
 * @param {*} rule
 * @param {*} value
 * @param {*} callback
 * @returns
 */
export function validateOrganizationCode(rule, value, callback) {
  if (value) {
    // if (!/[A-Z0-9]{8}-[A-Z0-9]$|[A-Z0-9]{8}-[A-Z0-9]-[0-9]{2}$/.test(value)) {
    if (!/[A-Z0-9]{8}-[A-Z0-9]$|[A-Z0-9]{8}[A-Z0-9]$/.test(value)) {
      callback(new Error('组织机构代码格式不正确'))
    } else {
      callback()
    }
  } else {
    callback()
  }
}
/**
 * 验证身份证号
 * @param {*} rule
 * @param {*} value
 * @param {*} callback
 * @returns
 */
export function validatecardNum(rule, value, callback) {
  if (value) {
    value = value.toUpperCase() // 对身份证号码做处理
    let ereg
    /* 身份号码位数及格式检验*/
    if (value.length === 15) {
      if (
        (parseInt(value.substr(6, 2), 10) + 1900) % 4 === 0 ||
                ((parseInt(value.substr(6, 2), 10) + 1900) % 100 === 0 &&
                    (parseInt(value.substr(6, 2), 10) + 1900) % 4 === 0)
      ) {
        ereg = /^[1-9][0-9]{5}[0-9]{2}((01|03|05|07|08|10|12)(0[1-9]|[1-2][0-9]|3[0-1])|(04|06|09|11)(0[1-9]|[1-2][0-9]|30)|02(0[1-9]|[1-2][0-9]))[0-9]{3}$/ // 测试出生日期的合法性
      } else {
        ereg = /^[1-9][0-9]{5}[0-9]{2}((01|03|05|07|08|10|12)(0[1-9]|[1-2][0-9]|3[0-1])|(04|06|09|11)(0[1-9]|[1-2][0-9]|30)|02(0[1-9]|1[0-9]|2[0-8]))[0-9]{3}$/ // 测试出生日期的合法性
      }
      if (ereg.test(value)) {
        callback()
      }
      callback(new Error('请输入正确的身份证号'))
    }
    if (!/^\d{6}(18|19|20)?\d{2}(0[1-9]|1[012])(0[1-9]|[12]\d|3[01])\d{3}(\d|X)$/i.test(value)) {
      callback(new Error('请输入正确的身份证号'))
      return
    }
    if (value.length === 18) {
      const city = {
        11: '北京', 12: '天津', 13: '河北', 14: '山西', 15: '内蒙古', 21: '辽宁', 22: '吉林', 23: '黑龙江 ', 31: '上海', 32: '江苏', 33: '浙江', 34: '安徽', 35: '福建', 36: '江西', 37: '山东', 41: '河南', 42: '湖北 ', 43: '湖南', 44: '广东', 45: '广西', 46: '海南', 50: '重庆', 51: '四川', 52: '贵州', 53: '云南', 54: '西藏 ', 61: '陕西', 62: '甘肃', 63: '青海', 64: '宁夏', 65: '新疆', 71: '台湾', 81: '香港', 82: '澳门', 91: '国外'
      }
      const birthday = `${value.substr(6, 4)}/${Number(value.substr(10, 2))}/${Number(value.substr(12, 2))}`
      const d = new Date(birthday)
      const newBirthday = `${d.getFullYear()}/${Number(d.getMonth() + 1)}/${Number(d.getDate())}`
      const currentTime = new Date().getTime()
      const time = d.getTime()
      const arrInt = [7, 9, 10, 5, 8, 4, 2, 1, 6, 3, 7, 9, 10, 5, 8, 4, 2]
      const arrCh = ['1', '0', 'X', '9', '8', '7', '6', '5', '4', '3', '2']
      let sum = 0; let i

      if (!/^\d{17}(\d|x)$/i.test(value)) callback(new Error('请输入正确的身份证号'))
      if (city[value.substr(0, 2)] === undefined) callback(new Error('请输入正确的身份证号'))
      if (time >= currentTime || birthday !== newBirthday) callback(new Error('请输入正确的身份证号'))
      for (i = 0; i < 17; i++) {
        sum += value.substr(i, 1) * arrInt[i]
      }
      const residue = arrCh[sum % 11]
      if (residue !== value.substr(17, 1)) callback(new Error('请输入正确的身份证号'))
      callback()
    }
  } else {
    callback(new Error('请输入身份证号'))
  }
}

/**
 * 验证手机和座机号码
 * @param {*} rule
 * @param {*} value
 * @param {*} callback
 * @returns
 */
export function validatePhoneNumbers(rule, value, callback) {
  if (value) {
    if (!/^((0\d{2,3}-?\d{7,8})|(13[0-9]|14[579]|15[0-3,5-9]|16[6]|17[0135678]|18[0-9]|19[89])\d{8})$/.test(value)) {
      callback(new Error('请输入正确的号码'))
    } else {
      callback()
    }
  } else {
    callback()
  }
}
