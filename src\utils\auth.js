import Cookies from 'js-cookie'

const TokenKey = 'governance_token'
const UserInfoKey = 'userInfo'

export function getToken() {
  return Cookies.get(TokenKey)
}

export function setToken(token) {
  return Cookies.set(To<PERSON><PERSON><PERSON>, token)
}

export function removeToken() {
  return Cookies.remove(TokenKey)
}

export function getUserInfo() {
  return Cookies.get(UserInfoKey)
}

export function setUserInfo(UserInfo) {
  return Cookies.set(UserInfoKey, UserInfo)
}

