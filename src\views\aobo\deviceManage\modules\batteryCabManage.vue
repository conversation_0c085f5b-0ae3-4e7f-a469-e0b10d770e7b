<template>
  <div>
    <div class="top">
      <div class="left">
        <img
          src="@/assets/device_manage/<EMAIL>"
          @click="back"
        >
        <div class="pa_name">空间与设备管理</div>
        <div class="ch_name">/</div>
        <div class="ch_name">工作面管理</div>
      </div>
      <div class="right">
        <el-button
          type="primary"
          @click="batchImport"
        >批量导入
        </el-button>
      </div>
    </div>
    <div class="content">
      <div
        class="content_left"
      >
        <div class="title">
          <div class="title_name">工作面列表</div>
          <div style="margin-right:20px;">
            <el-button
              type="primary"
              size="small"
              @click="addCab"
            >新增</el-button>
            <!--            <el-button-->
            <!--              type="warning"-->
            <!--              size="small"-->
            <!--              @click="locate"-->
            <!--            >定位</el-button>-->
          </div>
        </div>
        <div
          class="list"
          :style="{height: innerHeight - 400 + 'px'}"
        >
          <div
            v-for="item in cabList"
            :key="item.name"
            class="list_item"
            :class="selectCab.name === item.name ? 'isActive' : ''"
            @click="cabClick(item)"
          >
            <div class="list_item_content">
              <div class="cab_name">{{ item.name }}</div>
              <div class="btn">
                <img
                  src="@/assets/device_manage/<EMAIL>"
                  @click="editCab(item)"
                >
                <img
                  src="@/assets/device_manage/<EMAIL>"
                  @click="delCab(item)"
                >
              </div>
            </div>
          </div>
        </div>
        <el-pagination
          class="pagination"
          background
          :current-page.sync="pageNum"
          layout="total,prev,pager,next"
          :pager-count="3"
          :total="total"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>

    <!-- 删除工作面 -->
    <el-dialog
      title="删除"
      :visible.sync="dialogVisibleDel"
      :modal-append-to-body="false"
      width="500px"
      top="300px"
      @close="closeDelDialog()"
    >
      <div style="display:flex;align-items:center;padding-left:50px">
        <img
          src="@/assets/<EMAIL>"
          style="width:20px;height:20px;margin-right:10px"
        >
        <div style="font-size:16px;color:#F94E4E">
          确认删除该{{ `${delType === 1 ? '工作面' : '电池层'}` }}吗？
        </div>
      </div>
      <div
        slot="footer"
        class="dialog_footer"
      >
        <el-button @click="closeDelDialog()">取 消</el-button>
        <el-button
          type="primary"
          @click="handleDel()"
        >确 认</el-button>
      </div>
    </el-dialog>

    <!-- 新增工作面 -->
    <el-dialog
      :title="cabDialogTitle"
      :visible.sync="dialogVisibleCab"
      width="500px"
      :modal-append-to-body="false"
      top="170px"
      @close="closeCabDialog()"
    >
      <el-form
        ref="cabForm"
        :model="formDataCab"
        :rules="rules1"
        label-width="120px"
      >
        <el-form-item
          label="工作面编号:"
          prop="serialNum"
        >
          <el-input
            v-model="formDataCab.serialNum"
            placeholder="请输入"
            size="large"
            style="width:300px;"
            :disabled="formDataCab.id"
            @keyup.native="formDataCab.serialNum=numberInput(formDataCab.serialNum)
            "
          />
        </el-form-item>
        <el-form-item
          label="工作面名称:"
          prop="name"
        >
          <el-input
            v-model="formDataCab.name"
            placeholder="请输入"
            size="large"
            style="width:300px;"
          />
        </el-form-item>
        <el-form-item
          label="底图:"
          prop="image"
        >
          <div style="display: flex;flex-direction: column;gap: 0 10px;line-height: 20px;font-size: 12px">
            <UploadSingleImage v-model="formDataCab.image" />
            <span>支持.jpg, .png, .jpeg, .svg, .webp，建议上传svg矢量图片</span>
          </div>
        </el-form-item>
        <el-form-item
          label="底图比例:"
          prop="ratio"
          placeholder="请输入"
          size="large"
          style="width:300px;"
        >
          <el-input-number
            v-model="formDataCab.ratio"
            :min="0.1"
            :step="0.1"
          />
        </el-form-item>
      </el-form>
      <div
        slot="footer"
        class="dialog_footer"
      >
        <el-button
          type="primary"
          plain
          style="margin-right:10px"
          @click="closeCabDialog()"
        >取 消</el-button>
        <el-button
          type="primary"
          @click="handleSubmitCab()"
        >确 定</el-button>
      </div>
    </el-dialog>

    <!-- 新增电池层 -->
    <el-dialog
      title="新增"
      :visible.sync="dialogVisibleTier"
      width="500px"
      :modal-append-to-body="false"
      top="300px"
      @close="closeTierDialog()"
    >
      <el-form
        ref="tierForm"
        :model="formDataTier"
        :rules="rules2"
        label-width="120px"
      >
        <el-form-item
          label="本层行数:"
          prop="row"
        >
          <el-input
            v-model="formDataTier.row"
            placeholder="请输入"
            size="large"
            style="width:300px;"
            @keyup.native="
              formDataTier.row = integerInput(
                formDataTier.row
              )
            "
          />
        </el-form-item>
        <el-form-item
          label="本层列数:"
          prop="column"
        >
          <el-input
            v-model="formDataTier.column"
            placeholder="请输入"
            size="large"
            style="width:300px;"
            @keyup.native="
              formDataTier.column = integerInput(
                formDataTier.column
              )
            "
          />
        </el-form-item>
      </el-form>
      <div
        slot="footer"
        class="dialog_footer"
      >
        <el-button
          type="primary"
          plain
          style="margin-right:10px"
          @click="closeTierDialog()"
        >取 消</el-button>
        <el-button
          type="primary"
          @click="handleSubmitTier()"
        >确 定</el-button>
      </div>
    </el-dialog>

    <!-- 导入 -->
    <el-dialog
      title="导入"
      :visible.sync="importDialogVisible"
      width="fit-content"
      :modal-append-to-body="false"
      top="30vh"
      @close="closeImportDialog()"
    >
      <div style="padding:0 30px 0;">
        <div style="display:flex;justify-content: space-between;margin-bottom: 30px;align-items: center">
          <div
            slot="tip"
            class="uploadText1"
          >选择文件
            <div class="uploadText2">(支持扩展名:xls、xlsx)</div>
          </div>
          <el-link
            type="primary"
            @click="downloadTemplate"
          >点击此处下载模板</el-link>
        </div>
        <el-upload
          class="upload-demo"
          action="#"
          :auto-upload="false"
          :show-file-list="false"
          drag
          :on-change="handleImportSubmit"
        >
          <i
            v-show="false"
            class="el-icon-upload"
          />

          <img
            src="@/assets/userManage/<EMAIL>"
            style="width: 25.8px;height: 24.5px;margin: 17% 0 13px;"
          >
          <div class="el-upload__text"><em>选择上传</em></div>
        </el-upload>
      </div>
    </el-dialog>

    <el-dialog
      :title="resultData.successTotal ? '导入成功' : '导入失败'"
      :visible.sync="dialogVisibleImport"
      width="480px"
      :before-close="handleCloseImport"
      :modal-append-to-body="false"
    >
      <import-result
        :result-data="resultData"
        @handleClose="handleCloseImport"
        @submitForm="submitFormImport"
        @downFail="downFail"
      />
    </el-dialog>
  </div>
</template>

<script>
import { deepClone, utf8to16 } from '@/utils'
import {
  cabinetPage,
  saveCabinet,
  tierAndCellList,
  addTier,
  delSpace,
  downloadCabinetTemplate,
  importCabinet
} from '@/api/deviceManage'
import importResult from '@/components/importResult'
import UploadSingleImage from '@/components/UploadImage/UploadSingleImage.vue'

export default {
  name: 'BatteryCabManage',
  components: { UploadSingleImage, importResult },
  props: {
    targetData: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      pageNum: 1,
      pageSize: 10,
      total: 0,
      cabList: [],
      selectCab: {},
      tierList: [],

      dialogVisibleCab: false,
      dialogVisibleDel: false,
      dialogVisibleTier: false,
      delType: 1,
      cabDialogTitle: '新增',
      formDataCab: {
        name: '',
        serialNum: '',
        ratio: 1
      },
      formDataTier: {
        row: '',
        column: ''
      },
      rules1: {
        serialNum: [
          { required: true, message: '请输入工作面编号', trigger: 'blur' }
        ],
        name: [
          { required: true, message: '请输入工作面名称', trigger: 'blur' }
        ]
      },
      rules2: {
        row: [
          { required: true, message: '请输入本层行数', trigger: 'blur' }
        ],
        column: [
          { required: true, message: '请输入本层列数', trigger: 'blur' }
        ]
      },
      importDialogVisible: false,
      resultData: {}, // 导入结果
      dialogVisibleImport: false,
      innerHeight: null
    }
  },
  mounted() {
    this.getCabList()
    this.innerHeight = window.innerHeight
    window.onresize = () => {
      this.innerHeight = window.innerHeight
    }
  },
  methods: {
    // 获取工作面列表
    getCabList(isPage) {
      if (isPage) {
        this.pageNum = 1
        this.selectList = []
      }
      const params = {
        pageNum: this.pageNum,
        pageSize: this.pageSize,
        query: { parentSerialNum: `1${this.targetData.serialNum}` }
      }
      cabinetPage(params).then((res) => {
        this.cabList = res.data.records.map((item) => item)
        this.pageNum = res.data.current
        this.total = Number(res.data.total)
        this.selectCab = deepClone(this.cabList[0])
        this.selectCab && this.cabClick(this.selectCab)
      })
    },
    handleCurrentChange(val) {
      this.pageNum = val
      this.getCabList()
    },
    // 批量导入
    batchImport() {
      this.importDialogVisible = true
    },
    closeImportDialog() {
      this.importDialogVisible = false
    },
    // 下载模板
    downloadTemplate() {
      downloadCabinetTemplate().then((res) => {
        const url = window.URL.createObjectURL(
          new Blob([res.data], {
            type: 'application/vnd.ms-excel;charset=UTF-8'
          })
        )
        const temp = res.headers['content-disposition']
          .split(';')[1]
          .split('filename=')[1]
        const index = temp.indexOf('.')
        const str = temp.substr(0, index)
        const fileName = `${utf8to16(unescape(str))}.xlsx`
        const link = document.createElement('a')
        link.href = url
        link.download = fileName
        link.click()
        this.$message({
          type: 'success',
          message: '下载成功'
        })
      })
    },
    // 导入
    handleImportSubmit(file) {
      const blob = file.raw
      const type = blob.name.split('.')[1]
      if (type !== 'xlsx') {
        this.$message.warning(
          '导入文件只能是.xlsx结尾的文件，请检查上传文件是否是从本页面下载的模板'
        )
      } else {
        const loading = this.$loading({
          lock: true,
          text: '上传中',
          spinner: 'el-icon-loading',
          background: 'rgba(0, 0, 0, 0.7)'
        })
        const formData = new FormData()
        formData.append('file', blob)
        importCabinet(formData, this.opticalFiberId)
          .then((res) => {
            this.importDialogVisible = false
            this.resultData = res.data
            this.dialogVisibleImport = true
          }).finally(() => {
            loading.close()
          })
      }
    },
    handleCloseImport() {
      this.getCabList()
      this.dialogVisibleImport = false
    },
    submitFormImport() {
      this.getCabList()
      this.dialogVisibleImport = false
    },
    // 下载失败数据
    downFail() {
      const oReq = new XMLHttpRequest()
      oReq.open('GET', this.resultData.failExcelUrl, true)
      oReq.responseType = 'arraybuffer'
      oReq.withCredentials = true
      // eslint-disable-next-line func-names
      oReq.onload = function() {
        const arraybuffer = oReq.response
        if (arraybuffer) {
          const byteBUffer = new Uint8Array(arraybuffer)
          const link = document.createElement('a')
          const blob = new Blob([byteBUffer], {
            type: 'application/vnd.ms-excel;charset=UTF-8'
          })
          link.href = URL.createObjectURL(blob)
          link.download = '导入失败数据.xlsx'
          link.click()
        }
      }
      oReq.send()
      this.$message({
        type: 'success',
        message: '下载成功'
      })
    },
    // 新增工作面
    addCab() {
      this.cabDialogTitle = '新增'
      this.formDataCab = { ratio: 1 }
      this.dialogVisibleCab = true
    },
    locate() {
      this.$emit('changePage', {
        type: 'locate',
        data: this.targetData
      })
    },
    // 编辑工作面
    editCab(e) {
      this.cabDialogTitle = '编辑'
      this.formDataCab = deepClone(e)
      this.formDataCab.serialNum = this.formDataCab.serialNum.slice(4, 7)
      this.dialogVisibleCab = true
    },
    // 工作面提交
    handleSubmitCab() {
      this.$refs.cabForm.validate((valid) => {
        if (valid) {
          if (this.formDataCab.serialNum.length !== 3) {
            this.$message.warning('请输入3位工作面编号')
            return
          }
          const data = {
            ...this.formDataCab,
            imageBase64: this.formDataCab.image,
            parentSerialNum: Number(`1${this.targetData.serialNum}`)
          }
          data.image = undefined
          // const httpRqs = this.formDataCab.id ? saveCabinet : addCabinet
          saveCabinet(data).then((res) => {
            if (res.code === 200) {
              this.$message.success('成功')
              this.getCabList()
              this.closeCabDialog()
            }
          })
        }
      })
    },
    // 工作面弹窗关闭
    closeCabDialog() {
      this.formDataCab = {
        name: '',
        serialNum: ''
      }
      this.dialogVisibleCab = false
    },
    // 删除工作面
    delCab(e) {
      this.delType = 1
      this.selectCab = deepClone(e)
      this.dialogVisibleDel = true
    },
    // 删除提交
    handleDel() {
      // const http = this.delType === 1 ? null : null
      const data = this.delType === 1 ? [this.selectCab.id] : [this.tierList[this.tierList.length - 1].id]
      delSpace(data).then((res) => {
        if (res.code === 200) {
          this.$message.success('成功')
          this.closeDelDialog()
          if (this.delType === 1) {
            if (this.pageNum > 1 && this.cabList.length === 1) {
              this.pageNum--
            }
            this.getCabList()
          } else {
            this.cabClick(this.selectCab)
          }
        }
      })
    },
    // 删除工作面弹窗关闭
    closeDelDialog() {
      this.dialogVisibleDel = false
    },
    // 工作面点击
    cabClick(e) {
      this.tierList = []
      this.selectCab = deepClone(e)
    },
    // 新增电池层
    addTier() {
      if (this.tierList.length >= 6) {
        this.$message.warning('最多只能添加6层')
        return
      }
      this.dialogVisibleTier = true
    },
    // 电池层提交
    handleSubmitTier() {
      if (this.formDataTier.row * this.formDataTier.column > 26) {
        this.$message.warning('一层最多添加26个电池')
        return
      }
      this.$refs.tierForm.validate((valid) => {
        if (valid) {
          const data = {
            cabinetSerialNum: this.selectCab.serialNum,
            column: this.formDataTier.column,
            row: this.formDataTier.row
          }
          addTier(data).then((res) => {
            if (res.code === 200) {
              this.$message.success('成功')
              this.cabClick(this.selectCab)
              this.closeTierDialog()
            }
          })
        }
      })
    },
    // 电池层弹窗关闭
    closeTierDialog() {
      this.formDataTier = {
        row: '',
        column: ''
      }
      this.dialogVisibleTier = false
    },
    // 删除电池层
    delTier() {
      this.delType = 2
      this.dialogVisibleDel = true
    },
    // 限制输入正整数
    integerInput(num) {
      const val = num.replace(/[^0-9]/g, '')
      if (val.indexOf(0) === 0) {
        return ''
      }
      return val
    },
    // 只能输入数字
    numberInput(num) {
      const val = num.replace(/[^0-9]/g, '')
      return val
    },
    back() {
      this.$emit('changePage', { type: 'firstPage', data: { page: '1' }})
    }
  }
}
</script>

<style lang="scss" scoped>
.top {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-bottom: 15px;
  margin-bottom: 30px;
  border-bottom: 1px solid #E5E5E5;
  .left {
    display: flex;
    align-items: center;
    font-size: 17px;
    font-weight: bold;
    font-family: PingFang SC RE;
    img {
      width: 28px;
      height: 28px;
      cursor: pointer;
      margin-right: 10px;
    }
    .pa_name {
      color: #8D95A5;
      margin-right: 5px;
    }
    .ch_name {
      color: #202225;
      margin-right: 5px;
    }
  }
}
.content {
  display: flex;
  justify-content: space-between;
  font-family: PingFang SC RE;
  .title {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-bottom: 20px;
    border-bottom: 1px solid #E5E5E5;
    .title_name {
      border-left: 4px solid #1768EB;
      padding-left: 10px;
      font-size: 18px;
      font-weight: bold;
      color: #202225;
    }
  }
  .content_left {
    width: 100%;
    border: 1px solid #E5E5E5;
    border-radius: 3px;
    padding: 20px 0 60px 0;
    position: relative;
    .list {
      position: relative;
      overflow: auto;
      .list_item {
        padding: 0 15px;
        cursor: pointer;
        .list_item_content {
          height: 67px;
          display: flex;
          justify-content: space-between;
          align-items: center;
          border-bottom: 1px solid #E5E5E5;
          .cab_name {
            font-weight: bold;
            width: 250px;
            text-overflow: ellipsis;
            overflow: hidden;
            white-space: nowrap;
          }
          .btn {
            img {
              width: 20px;
              height: 20px;
              cursor: pointer;
              &:nth-child(1) {
                margin-right: 30px;
              }
            }
          }
        }
      }
      .isActive {
        border-left: 4px solid #5D9BFF;
        background: #EDF5FF;
      }
    }
    .pagination {
      position: absolute;
      bottom: 20px;
      right: 10px;
    }
  }
}
</style>
<style lang="scss">
.el-popover {
  background: black !important;
  border-color: black;
  opacity: 0.8 !important;
  color: #fff !important;
  height: 40px !important;
  padding: 10px !important;
  text-align: center !important;
  .popper__arrow {
    border-top-color: black !important;
    opacity: 0.8 !important;
    bottom: -5px !important;
    &::after {
      bottom: 1px !important;
      border-top-color: black !important;
      border-bottom-width: 0 !important;
    }
  }
}
</style>
