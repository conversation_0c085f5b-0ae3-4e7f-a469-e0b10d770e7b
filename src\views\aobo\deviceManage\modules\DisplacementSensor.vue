<script>
import {
  add,
  del, deviceExport, page, update
} from '@/api/displacementSensor'
import { mapGetters } from 'vuex'
import { deepClone, flatData, utf8to16 } from '@/utils'
import LocatePoint from '@/components/LocateDialog/LocatePoint'
import { getCabinet, getPointTree, } from '@/api/deviceManage'

export default {
  name: 'DisplacementSensor',
  components: { LocatePoint },
  props: {
    btnAuthorityList: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      total: 0,
      loading: false,
      query: {
        keyword: '',
        pageNum: 1,
        pageSize: 10
      },
      dataList: [],
      selectList: [],
      isDetail: false,
      formData: null,
      dialogVisible: false,
      dialogVisibleDel: false,
      flatList: [],
      title: '',
      rules: {
        name: [
          { required: true, message: '请输入设备名称', trigger: 'blur' }
        ],
        code: [{ required: true, message: '请输入设备编号', trigger: 'blur' }],
        workFaceId: [{ required: true, message: '请选择工作面', trigger: 'blur' }],
        pointNum: [{ required: true, message: '请输入点位数量', trigger: 'blur' }],
      },

      dropdownList: [], // 煤矿下拉框
      workFaceDetail: null, // 所选工作面详情
      workFaceDetailLoading: false,
      locateDialogVisible: false, // 标绘弹窗
    }
  },
  computed: {
    ...mapGetters(['tableHeaderStyle'])
  },

  created() {
    this.handleQuery()
    this.workFaceDropdown()
  },

  methods: {
    handleQuery() {
      this.loading = true
      page(this.query).then((res) => {
        console.log(res)
        this.dataList = res.data.records
      }).finally(() => {
        this.loading = false
      })
    },

    // 区域下拉框
    workFaceDropdown() {
      getPointTree().then((res) => {
        this.dropdownList = res.data
        this.flatList = flatData(res.data)
      })
    },

    // 切换工作面
    workFaceChange() {
      const cur = this.flatList.find((item) => item.id === this.formData.workFaceId)
      if (cur.spaceType !== 2) {
        this.$message.warning('请选择工作面')
        return
      }
      this.workFaceDetailLoading = true
      this.workFaceDetail = null
      getCabinet(this.formData.workFaceId).then((res) => {
        this.workFaceDetail = res.data
      }).finally(() => {
        this.workFaceDetailLoading = false
      })
    },

    search() {
      this.pageNum = 1
      this.handleQuery()
    },
    // 分页
    handleSizeChange(val) {
      this.query.pageSize = val
      this.handleQuery()
    },
    handleCurrentChange(val) {
      this.query.pageNum = val
      this.handleQuery()
    },
    // 多选改变
    handleSelectionChange(list) {
      this.selectList = deepClone(list)
    },

    // 新增DTS
    add() {
      this.isDetail = false
      this.formData = {}
      this.title = '新增'
      this.dialogVisible = true
    },

    // 编辑
    edit(e) {
      this.isDetail = false
      this.title = '编辑'
      this.formData = deepClone(e)
      this.workFaceChange()
      this.dialogVisible = true
    },

    // 提交
    handleSubmit() {
      if (!this.formData.points?.length) {
        this.$message.warning('请标绘')
        return
      }
      if (this.formData.points?.length > this.formData.pointNum) {
        this.$message.warning('标绘点位数量不能大于点位数量，请重新标绘或修改点位数量')
        return
      }
      this.$refs.form.validate((valid) => {
        if (valid) {
          const data = {
            ...this.formData
          }
          const httpRqs = this.formData.id ? update : add
          httpRqs(data).then((res) => {
            if (res.code === 200) {
              this.$message.success('成功')
              this.handleQuery()
              this.dialogVisible = false
            }
          })
        }
      })
    },

    // 详情
    detail(e) {
      this.isDetail = true
      this.title = '详情'
      this.formData = deepClone(e)
      console.log(deepClone(e))
      this.workFaceChange()
      this.dialogVisible = true
    },
    // 删除DTS
    remove(e) {
      this.formData = deepClone(e)
      this.dialogVisibleDel = true
    },

    // 批量删除
    batchDel() {
      if (!this.selectList.length) {
        this.$message.warning('请至少选择一条数据')
        return
      }
      this.formData = null
      this.dialogVisibleDel = true
    },

    // 确认删除
    handleDel() {
      const idList = this.formData ? [this.formData.id] : this.selectList.map((item) => item.id)
      del(idList).then((res) => {
        if (res.code === 200) {
          this.$message.success('成功')
          // 判断是否需要返回上一页
          if (this.query.pageNum > 1 && (idList.length >= this.dataList.length)) {
            this.query.pageNum--
          }

          this.handleQuery()
          this.dialogVisibleDel = false
        }
      })
    },

    // 批量导出
    batchExport() {
      deviceExport(this.query).then((res) => {
        const url = window.URL.createObjectURL(
          new Blob([res.data], {
            type: 'application/vnd.ms-excel;charset=UTF-8'
          })
        )
        const temp = res.headers['content-disposition']
          .split(';')[1]
          .split('filename=')[1]
        const index = temp.indexOf('.')
        const str = temp.substr(0, index)
        const fileName = `${utf8to16(unescape(str))}.xlsx`
        const link = document.createElement('a')
        link.href = url
        link.download = fileName
        link.click()
        this.$message({
          type: 'success',
          message: '导出成功'
        })
      })
    },

    locate() {
      if (!this.formData.workFaceId) {
        this.$message.warning('请先选择工作面')
        return
      }

      if (!this.workFaceDetail.image) {
        this.$message.warning('该工作面没有底图!')
        return
      }

      if (!this.formData.pointNum) {
        this.$message.warning('请先输入点位数量')
        return
      }

      this.locateDialogVisible = true
    },

    // 确定标绘
    changePoint() {
      const { locateRef } = this.$refs
      if (!locateRef.validate()) return
      this.formData.points = locateRef.getPoints().map((item) => ({ x: item.lat, y: item.lng, name: item.name }))
      this.locateDialogVisible = false
    }
  }
}

</script>

<template>
  <div
    v-loading="loading"
    class="dts_manage"
  >
    <div class="top">
      <el-input
        v-model="query.keyword"
        placeholder="请输入搜素内容"
        suffix-icon="el-icon-search"
        clearable
        style="width:300px;"
        @change="search"
      />
      <div class="btn_list">
        <el-button
          type="primary"
          size="small"
          :disabled="!btnAuthorityList.DTSManageBtn.includes('add')"
          @click="add"
        >新增</el-button>
        <el-button
          type="danger"
          size="small"
          :disabled="!btnAuthorityList.DTSManageBtn.includes('batch-del')"
          @click="batchDel"
        >批量删除</el-button>
        <!--        <el-button-->
        <!--          type="success"-->
        <!--          size="small"-->
        <!--          :disabled="!btnAuthorityList.DTSManageBtn.includes('export')"-->
        <!--          @click="batchExport"-->
        <!--        >批量导出</el-button>-->
      </div>
    </div>
    <el-table
      ref="multipleTable"
      v-loading="loading"
      :header-cell-style="tableHeaderStyle"
      header-row-class-name="table-header"
      :data="dataList"
      stripe
      height="45vh"
      row-key="id"
      @selection-change="handleSelectionChange"
    >
      <el-table-column
        type="selection"
        width="70"
      />
      <el-table-column
        label="序号"
        type="index"
        width="70"
        align="center"
      />
      <el-table-column
        prop="name"
        label="设备名称"
        show-overflow-tooltip
        align="center"
      />
      <el-table-column
        prop="code"
        label="设备编号"
        align="center"
        show-overflow-tooltip
      />
      <el-table-column
        prop="workFaceName"
        label="所属工作面"
        align="center"
        show-overflow-tooltip
      >
        <template #default="{row}">
          {{ row.mineName }}/{{ row.workFaceName }}
        </template>
      </el-table-column>

      <el-table-column
        prop="pointNum"
        label="点位数量"
        align="center"
        show-overflow-tooltip
      />

      <el-table-column
        prop="createTime"
        label="创建时间"
        align="center"
        show-overflow-tooltip
      />

      <el-table-column
        label="操作"
        fixed="right"
        align="center"
      >
        <template v-slot="scope">
          <el-button
            type="text"
            style="text-decoration:underline;margin-right:15px;"
            @click="detail(scope.row)"
          >详情</el-button>
          <el-button
            type="text"
            style="text-decoration:underline;margin-right:15px;"
            :style="{color: !btnAuthorityList.DTSManageBtn.includes('update') ? '' : '#67C23A'}"
            :disabled="!btnAuthorityList.DTSManageBtn.includes('update')"
            @click="edit(scope.row)"
          >编辑</el-button>
          <el-button
            type="text"
            style="text-decoration:underline;"
            :style="{color: !btnAuthorityList.DTSManageBtn.includes('del') ? '' : '#F56C6C'}"
            :disabled="!btnAuthorityList.DTSManageBtn.includes('del')"
            @click="remove(scope.row)"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination
      background
      :current-page.sync="query.pageNum"
      :page-size="query.pageSize"
      layout="total,prev, pager, next,sizes, jumper"
      :page-sizes="[10, 20, 50, 100]"
      :total="total"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
    />

    <!-- 新增测温点 -->
    <el-dialog
      v-if="dialogVisible"
      :title="title"
      :visible.sync="dialogVisible"
      width="fit-content"
      :close-on-click-modal="false"
      append-to-body
      destroy-on-close
    >
      <el-form
        ref="form"
        :model="formData"
        :rules="rules"
        :disabled="isDetail"
        label-width="120px"
        style="padding: 0 50px"
      >
        <el-form-item
          label="设备名称:"
          prop="name"
        >
          <el-input
            v-model="formData.name"
            placeholder="请输入"
            size="large"
            style="width:320px;"
          />
        </el-form-item>

        <el-form-item
          label="设备编号:"
          prop="code"
        >

          <el-input-number
            v-model="formData.code"
            :min="0"
            :controls="false"
            style="width:320px;"
            placeholder="请输入"
          />

        </el-form-item>

        <el-form-item
          label="工作面:"
          prop="workFaceId"
        >
          <el-cascader
            v-model="formData.workFaceId"
            style="width:320px;"
            :options="dropdownList"
            :props="{value: 'id', children: 'childList', label: 'name', emitPath: false }"
            @change="workFaceChange"
          />
        </el-form-item>

        <el-form-item
          prop="pointNum"
          label="点位数量:"
        >
          <el-input-number
            v-model="formData.pointNum"
            :min="1"
            :controls="false"
            style="width:320px;"
            placeholder="请输入"
          />
        </el-form-item>

        <el-form-item
          prop="historyInterval"
          label="历史数据保存间隔:"
        >
          <el-input-number
            v-model="formData.historyInterval"
            :min="1"
            :controls="false"
            style="width:320px;"
            placeholder="请输入"
          />
          <span style="margin-left: 10px;">秒</span>
        </el-form-item>

        <el-form-item
          label="标绘:"
          prop="pointCellType"
        >
          <el-button
            type="primary"
            :disabled="false"
            :loading="workFaceDetailLoading"
            @click="locate"
          >标绘</el-button>
        </el-form-item>
      </el-form>
      <div
        slot="footer"
        class="dialog_footer"
      >
        <el-button
          v-if="!isDetail"
          type="primary"
          plain
          style="margin-right:10px"
          @click="dialogVisible = false"
        >取 消
        </el-button>
        <el-button
          v-if="isDetail"
          type="primary"
          plain
          style="margin-right:10px"
          @click="dialogVisible = false"
        >关 闭
        </el-button>
        <el-button
          v-if="!isDetail"
          type="primary"
          @click="handleSubmit"
        >确 定
        </el-button>
      </div>
    </el-dialog>

    <!--    标绘弹窗-->
    <el-dialog
      v-if="locateDialogVisible"
      :visible.sync="locateDialogVisible"
      width="fit-content"
      title="标绘"
      top="2vh"
      :close-on-click-modal="false"
      append-to-body
      destroy-on-close
    >
      <LocatePoint
        ref="locateRef"
        :is-detail="isDetail"
        :image="workFaceDetail.image"
        :points="formData.points"
        :point-length="formData.pointNum"
      />
      <template #footer>
        <el-button @click="locateDialogVisible = false">取消</el-button>
        <el-button
          type="primary"
          @click="changePoint"
        >确定</el-button>
      </template>
    </el-dialog>

    <!-- 删除 -->
    <el-dialog
      title="删除"
      :visible.sync="dialogVisibleDel"
      :close-on-click-modal="false"
      append-to-body
      destroy-on-close
      width="500px"
      top="300px"
    >
      <div style="display:flex;align-items:center;padding-left:50px">
        <img
          src="@/assets/<EMAIL>"
          style="width:20px;height:20px;margin-right:10px"
        >
        <div style="font-size:16px;color:#F94E4E">
          确认删除所选数据吗？
        </div>
      </div>
      <div
        slot="footer"
        class="dialog_footer"
      >
        <el-button @click="dialogVisibleDel = false">取 消</el-button>
        <el-button
          type="primary"
          @click="handleDel"
        >确 认</el-button>
      </div>
    </el-dialog>

  </div>
</template>

<style scoped lang="scss">
.dts_manage {
  .top {
    display: flex;
    justify-content: space-between;
    margin: 10px 0 20px 0;
    .btn_list {
      display: flex;
    }
  }
}
</style>
