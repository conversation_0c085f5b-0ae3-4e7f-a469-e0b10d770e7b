<template>
  <div class="optical_fiber_manage">
    <div class="top">
      <div class="left">
        <img
          src="@/assets/device_manage/<EMAIL>"
          @click="back"
        >
        <div class="pa_name">DTS管理</div>
        <div class="ch_name">/</div>
        <div class="ch_name">光纤管理</div>
      </div>
    </div>
    <div class="base_info">
      <div class="base_info_content">
        <div class="base_info_item">
          <div class="dot" />
          <div>DTS主机信息：{{ targetData.name || '--' }}</div>
        </div>
        <div class="base_info_item">
          <div class="dot" />
          <div>mac地址：{{ targetData.mac || '--' }}</div>
        </div>
        <div class="base_info_item">
          <div class="dot" />
          <div>刷新时间：{{ `${targetData.refreshInterval}s` }}</div>
        </div>
      </div>
    </div>
    <div>
      <div>
        <div class="title">
          <div>
            <el-button
              type="primary"
              @click="add"
            >新增
            </el-button>
          </div>
        </div>
        <div class="table">
          <el-table
            ref="multipleTable"
            v-loading="loading"
            :header-cell-style="tableHeaderStyle"
            header-row-class-name="table-header"
            :data="tableData"
            stripe
            :height="innerHeight"
          >
            <el-table-column
              label="序号"
              type="index"
              width="70"
              align="center"
            />
            <el-table-column
              prop="cableCode"
              label="光纤编号"
              show-overflow-tooltip
              align="center"
            >
              <template slot-scope="scope">
                <span>{{ scope.row.cableCode || '--' }}</span>
              </template>
            </el-table-column>
            <el-table-column
              prop="name"
              label="光纤名称"
              show-overflow-tooltip
              align="center"
            >
              <template slot-scope="scope">
                <span>{{ scope.row.name || '--' }}</span>
              </template>
            </el-table-column>
            <!--            <el-table-column-->
            <!--              prop="defaultTemp"-->
            <!--              label="初始温度"-->
            <!--              show-overflow-tooltip-->
            <!--              align=""-->
            <!--            >-->
            <!--              <template slot-scope="scope">-->
            <!--                <span>{{ scope.row.defaultTemp || '&#45;&#45;' }}</span>-->
            <!--              </template>-->
            <!--            </el-table-column>-->
            <el-table-column
              prop="resolutionRatio"
              label="采样率"
              align="center"
              show-overflow-tooltip
            >
              <template slot-scope="scope">
                <span>{{ scope.row.resolutionRatio || scope.row.resolutionRatio === 0 ?
                  `${scope.row.resolutionRatio / 100}m` : '--' }}</span>
                <!-- <span>{{ scope.row.resolutionRatio || scope.row.resolutionRatio === 0 ?
                  `${scope.row.resolutionRatio}m` : '--' }}</span> -->
              </template>
            </el-table-column>
            <el-table-column
              prop="cableLength"
              label="光纤长度"
              align="center"
              show-overflow-tooltip
            >
              <template slot-scope="scope">
                <span>{{ scope.row.cableLength || scope.row.cableLength === 0 ?
                  `${scope.row.cableLength / 100}m` : '--' }}</span>
                <!-- <span>{{ scope.row.cableLength || scope.row.cableLength === 0 ?
                  `${scope.row.cableLength}m` : '--' }}</span> -->
              </template>
            </el-table-column>
            <el-table-column
              prop="fibreCoreSize"
              label="纤芯类型"
              show-overflow-tooltip
              align="center"
            >
              <template slot-scope="scope">
                <span>{{ scope.row.fibreCoreSize || '--' }}</span>
              </template>
            </el-table-column>
            <el-table-column
              label="操作"
              fixed="right"
              align="center"
              width="160"
            >
              <template v-slot="scope">
                <el-button
                  type="text"
                  style="text-decoration:underline;margin-right:15px;"
                  @click="getDetail(scope.row, 2)"
                >详情
                </el-button>
                <el-button
                  type="text"
                  style="color:#67C23A;text-decoration:underline;margin-right:15px;"
                  @click="getDetail(scope.row, 3)"
                >编辑
                </el-button>
                <el-button
                  type="text"
                  style="color:#F56C6C;text-decoration:underline;"
                  @click="del(scope.row)"
                >删除
                </el-button>
              </template>
            </el-table-column>
          </el-table>
          <el-pagination
            background
            :current-page.sync="pageNum"
            :page-size="pageSize"
            layout="total,prev, pager, next,sizes, jumper"
            :page-sizes="[10, 20, 50, 100]"
            :total="total"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </div>
      </div>
    </div>
    <!-- 删除 -->
    <el-dialog
      title="删除"
      :visible.sync="dialogVisibleDel"
      :modal-append-to-body="false"
      width="500px"
      top="320px"
      @close="closeDelDialog()"
    >
      <div style="display:flex;align-items:center;padding-left:50px">
        <img
          src="@/assets/<EMAIL>"
          style="width:20px;height:20px;margin-right:10px"
        >
        <div style="font-size:16px;color:#F94E4E">
          确认删除所选数据吗？
        </div>
      </div>
      <div
        slot="footer"
        class="dialog_footer"
      >
        <el-button @click="closeDelDialog()">取 消</el-button>
        <el-button
          type="primary"
          @click="handleDel()"
        >确 认
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { deepClone } from '@/utils'
import { mapGetters } from 'vuex'
import {
  opticalFiberPage,
  opticalFiberDel
} from '@/api/deviceManage'

export default {
  name: 'BatteryCabManage',
  components: {},
  props: {
    targetData: {
      type: Object,
      default: () => {
      }
    }
  },
  data() {
    return {
      loading: false,
      tableData: [],
      pageNum: 1,
      pageSize: 10,
      total: 0,
      select: {},
      dialogVisibleDel: false,
      innerHeight: 500
    }
  },
  computed: {
    ...mapGetters(['tableHeaderStyle'])
  },
  mounted() {
    this.getList(1)
    window.onresize = () => {
      this.innerHeight = window.innerHeight - 430
    }
  },
  methods: {
    back() {
      this.$emit('changePage', { type: 'firstPage', data: { page: '2' }})
    },
    // 获取列表
    getList(isPage) {
      if (isPage) {
        this.pageNum = 1
        this.selectList = []
      }
      const params = {
        pageNum: this.pageNum,
        pageSize: this.pageSize,
        query: {
          deviceId: this.targetData.id
        }
      }
      this.loading = TextTrackCueList
      opticalFiberPage(params).then((res) => {
        this.tableData = res.data.records.map((item) => item)
        this.pageNum = res.data.current
        this.total = Number(res.data.total)
      }).finally(() => {
        this.loading = false
      })
    },
    // 分页
    handleSizeChange(val) {
      this.pageSize = val
      this.getList()
    },
    handleCurrentChange(val) {
      this.pageNum = val
      this.getList()
    },
    // 新增
    add() {
      const data = { type: 1, data: { deviceId: this.targetData.id, parentData: deepClone(this.targetData) }}
      this.$emit('changePage', {
        type: 'newopticalFiber',
        data
      })
    },
    // 详情、编辑
    getDetail(e, type) {
      const data = {
        type,
        data: {
          ...e,
          deviceId: this.targetData.id,
          opticalFiberId: e.id,
          parentData: deepClone(this.targetData)
        }
      }
      console.log(data)
      this.$emit('changePage', {
        type: 'newopticalFiber',
        data
      })
    },
    // 删除
    del(e) {
      this.select = deepClone(e)
      this.dialogVisibleDel = true
    },
    // 删除提交
    handleDel() {
      opticalFiberDel({ id: this.select.id }).then((res) => {
        if (res.code === 200) {
          this.$message.success('成功')
          if ((this.pageNum > 1) && this.tableData.length === 1) {
            this.pageNum--
          }
          this.getList()
          this.closeDelDialog()
        }
      })
    },
    // 删除取消
    closeDelDialog() {
      this.select = {}
      this.dialogVisibleDel = false
    }
  }
}
</script>

<style lang="scss" scoped>
.optical_fiber_manage {
  font-family: PingFang SC RE;
  .top {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-bottom: 15px;
    border-bottom: 1px solid #E5E5E5;

    .left {
      display: flex;
      align-items: center;
      font-size: 17px;
      font-weight: bold;

      img {
        width: 28px;
        height: 28px;
        cursor: pointer;
        margin-right: 10px;
      }

      .pa_name {
        color: #8D95A5;
        margin-right: 5px;
      }

      .ch_name {
        color: #202225;
        margin-right: 5px;
      }
    }
  }
  .base_info {
    width: 100%;
    border: 1px solid #E0E0E0;
    border-radius: 5px;
    padding: 20px;
    margin-bottom: 10px;
    .base_info_content {
      display: flex;
      justify-content: space-between;
      width: 60%;
      font-weight: bold;
      .base_info_item {
        display: flex;
        align-items: center;
        height: 23px;
        .dot {
          width: 8px;
          height: 8px;
          border-radius: 8px;
          background: #0E73F3;
          margin-right: 10px;
        }
      }
    }
  }
  .title {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    margin: 30px 0;
    width: 100%;
  }
}
</style>
