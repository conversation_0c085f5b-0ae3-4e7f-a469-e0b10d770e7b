// import Vue from 'vue'
import SparkMD5 from 'spark-md5'

// eslint-disable-next-line import/prefer-default-export
export function calcFileMD5(file) {
  return new Promise((resolve, reject) => {
    const chunkSize = 2097152 // 2M
    const chunks = Math.ceil(file.size / chunkSize)
    let currentChunk = 0
    const spark = new SparkMD5.ArrayBuffer()
    const fileReader = new FileReader()

    fileReader.onload = (e) => {
      spark.append(e.target.result)
      currentChunk++
      if (currentChunk < chunks) {
        // eslint-disable-next-line no-use-before-define
        loadNext()
      } else {
        const md5 = spark.end()
        resolve(md5)
      }
    }

    fileReader.onerror = (e) => {
      reject(fileReader.error)
      // eslint-disable-next-line no-undef
      reader.abort()
    }

    function loadNext() {
      const start = currentChunk * chunkSize
      const end =
          start + chunkSize >= file.size ? file.size : start + chunkSize
      fileReader.readAsArrayBuffer(file.slice(start, end))
    }
    loadNext()
  })
}
export async function asyncPool(poolLimit, array, iteratorFn) {
  const ret = []
  const executing = []
  for (const item of array) {
    const p = Promise.resolve().then(() => iteratorFn(item, array))
    ret.push(p)

    if (poolLimit <= array.length) {
      const e = p.then(() => executing.splice(executing.indexOf(e), 1))
      executing.push(e)
      if (executing.length >= poolLimit) {
        // eslint-disable-next-line no-await-in-loop
        await Promise.race(executing)
      }
    }
  }
  return Promise.all(ret)
}
