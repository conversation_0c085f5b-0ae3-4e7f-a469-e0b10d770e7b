<template>
  <div class="container">
    <!--    监控段告警策略配置-->
    <header class="top-title">
      <div class="title-label" />
      <div class="title">告警策略配置</div>
    </header>

    <el-table
      v-loading="tableLoading"
      :header-cell-style="{ color: '#595959', background: '#F9F9FA', fontSize: '14px !important', fontWeight: 400 }"
      :data="tableData"
      stripe
      border
      style="margin-top: 20px"
    >
      <el-table-column prop="alarmType" label="报警类型" show-overflow-tooltip align="center" min-width="100">
        <template slot-scope="scope">
          <span>{{ alarmTypeMap[scope.row.alarmType] }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="duration" label="持续时间(s)" show-overflow-tooltip align="center" min-width="100">
        <template slot-scope="scope">
          <div>
            <span>在</span>
            <el-input-number v-model="scope.row.duration" placeholder="请输入" :min="0" :controls="false" style="margin: 0 10px" />
            <span>秒内</span>
          </div>
        </template>
      </el-table-column>
      <el-table-column prop="levels" label="报警等级" align="center" min-width="200">
        <template slot-scope="scope">
          <div v-if="scope.row.alarmType === 2" class="level-box">
            <div v-for="(item, index) in scope.row.levels" :key="index" class="level-item">
              <span>出现</span>
              <el-input-number v-model="item.count" placeholder="请输入" :min="0" :controls="false" size="small" />
              <span>次，位移大于或等于</span>
              <el-input-number v-model="item.value" placeholder="请输入" :min="0" :controls="false" size="small" />
              <span>mm，产生</span>
              <el-select v-model="item.level" placeholder="报警等级" size="small">
                <el-option v-for="(label, value) in alarmLevelMap" :key="value" :label="label" :value="Number(value)" />
              </el-select>
              <el-button
                type="danger"
                icon="el-icon-delete"
                circle
                size="mini"
                @click="deleteLevel(scope.row.levels, index)"
              />
            </div>
            <div>
              <el-button type="primary" icon="el-icon-plus" circle size="mini" @click="addLevel(scope.row.levels)" />
            </div>
          </div>
          <div v-else class="level-box">
            <div v-for="(item, index) in scope.row.levels" :key="index" class="level-item">
              <span>温度持续大于或等于</span>
              <el-input-number v-model="item.value" placeholder="请输入" :min="0" :controls="false" size="small" />
              <span>℃，产生</span>
              <el-select v-model="item.level" placeholder="报警等级" size="small">
                <el-option v-for="(label, value) in alarmLevelMap" :key="value" :label="label" :value="Number(value)" />
              </el-select>
              <el-button
                type="danger"
                icon="el-icon-delete"
                circle
                size="mini"
                @click="deleteLevel(scope.row.levels, index)"
              />
            </div>
            <div>
              <el-button type="primary" icon="el-icon-plus" circle size="mini" @click="addLevel(scope.row.levels)" />
            </div>
          </div>
        </template>
      </el-table-column>
    </el-table>
    <footer class="footer" style="margin-top: 20px">
      <el-button type="primary" :loading="submitLevelLoading" @click="saveAlarmLevel">保存</el-button>
    </footer>

    <div v-if="false" class="form-box">
      <div class="item-row">
        <span class="label-item">报警阈值：</span>
        <span style="margin-left: 0" class="label-item"> 温度大于或等于</span>
        <el-input-number v-model="data.threshold" placeholder="请输入" :controls="false" />
        <span class="label-item">摄氏度，并持续</span>
        <el-input-number v-model="data.lengthTime" placeholder="请输入" :min="0" :controls="false" />
        <span class="label-item">秒，产生报警信息。</span>
      </div>

      <div class="item-row">
        <span class="label-item">偏差报警：</span>
        <span style="margin-left: 0" class="label-item"> 偏差大于或等于</span>
        <el-input-number v-model="data.deviation" placeholder="请输入" :controls="false" />
        <span class="label-item">摄氏度，并持续</span>
        <el-input-number v-model="data.lengthTime2" placeholder="请输入" :min="0" :controls="false" />
        <span class="label-item">秒，产生报警信息。</span>
      </div>
      <!--      <div class="item-row">-->
      <!--        <el-radio-->
      <!--          v-model="type"-->
      <!--          :label="0"-->
      <!--          class="radio"-->
      <!--        >是否覆盖</el-radio>-->

      <!--      </div>-->
    </div>
    <!--    提示音频配置-->
    <header class="top-title" style="margin-bottom: 25px">
      <div class="title-label" />
      <div class="title">提示音频配置</div>
    </header>
    <table v-loading="loading" class="audio-table">
      <thead>
        <tr>
          <th>启用声音提示</th>
          <th>提示播放次数</th>
          <th>
            <div>音频操作</div>
            <div>(可上传wav、mp3等音频格式)</div>
          </th>
          <th>音频文件</th>
        </tr>
      </thead>
      <tbody>
        <tr>
          <td>
            <el-radio v-model="data.isEnableAudio" :label="0">否</el-radio>
            <el-radio v-model="data.isEnableAudio" :label="1">是</el-radio>
          </td>
          <td :class="{ disabled }">
            <span>连续</span>
            <el-input-number
              v-model="data.cycleCount"
              :min="1"
              :disabled="disabled"
              placeholder="请输入"
              style="width: 180px; margin: 0 10px"
              :controls="false"
            />
            <span>次</span>
          </td>
          <td :class="{ disabled }">
            <div class="audio-operate">
              <el-tooltip content="请先上传" placement="right" :disabled="!!data.audioUrl">
                <div class="item" :class="{ disabled: !data.audioUrl }" @click="playAudio">
                  <img src="@/assets/icon/<EMAIL>" class="icon" alt="试听">
                  <span class="text">试听</span>
                </div>
              </el-tooltip>

              <el-upload class="upload-demo" action="#" :disabled="disabled" :show-file-list="false" :auto-upload="false" :on-change="uploadAudio">
                <div class="item">
                  <img src="@/assets/icon/<EMAIL>" class="icon" alt="上传">
                  <span class="text">上传</span>
                  <span style="width: 20px">
                    <i v-if="uploading" class="el-icon-loading" />
                  </span>
                </div>
              </el-upload>
            </div>
          </td>
          <td :class="{ disabled }">{{ data.audioFileName }}</td>
        </tr>
      </tbody>
    </table>

    <!--    确定-->
    <footer class="footer">
      <el-button type="primary" :loading="submitLoading" @click="submit">保存</el-button>
    </footer>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import {
  getAlarmLevelList, getTactics, saveTactics, setTactics, uploadAudio
} from '@/api/alarmTactics'
import { playAudio } from '@/utils/palyAudio'
import { alarmLevelMap } from '@/constants'

export default {
  name: 'BaseConfig',
  data() {
    return {
      data: {},
      loading: false,
      submitLoading: false,
      closeAudio: null, // 关闭音频的函数
      uploading: false,
      tableData: [
        { alarmType: 0, duration: 60, levels: [] },
        { alarmType: 0, duration: 60, levels: [] },
        { alarmType: 0, duration: 60, levels: [] },
      ],
      // 报警等级下拉
      alarmTypeMap: {
        0: '温度报警',
        1: '偏差报警',
        2: '位移报警',
      },
      alarmLevelMap,
      tableLoading: false,
      submitLevelLoading: false,
    }
  },
  computed: {
    ...mapGetters(['tableHeaderStyle', 'btnAuthority']),
    disabled() {
      return this.data.isEnableAudio === 0
    },
  },
  created() {
    this.doQuery()
    this.getAlarmLevelList()
  },
  destroyed() {
    this.closeAudio?.()
  },
  methods: {
    /**
     * 获取页面初始数据
     * */
    doQuery() {
      this.loading = true
      getTactics()
        .then((res) => {
          if (res.data) {
            this.data = res.data
            this.data.threshold /= 100
            this.data.deviation /= 100
          }
        })
        .finally(() => {
          this.loading = false
        })
    },
    /**
     * 获取等级列表
     * */
    getAlarmLevelList() {
      this.tableLoading = true
      getAlarmLevelList()
        .then((res) => {
          this.tableData = res.data
        })
        .finally(() => {
          this.tableLoading = false
        })
    },
    /**
     * 移除告警等级
     * */
    deleteLevel(levels, index) {
      levels.splice(index, 1)
    },
    /**
     * 添加告警等级
     * */
    addLevel(levels) {
      levels.push({ count: 1, value: 0, level: 0 })
    },
    /**
     * 保存告警策略等级
     */
    saveAlarmLevel() {
      this.submitLevelLoading = true
      saveTactics(this.tableData)
        .then((res) => {
          if (res.code === 200) {
            this.getAlarmLevelList()
            this.$message.success('保存成功')
          }
        })
        .finally(() => {
          this.submitLevelLoading = false
        })
    },

    /**
     * 文件校验
     * */
    fileVerification(file) {
      /** 校验文件类型*/
      /** 合格文件类型：mp3、wav
       * @see https://developer.mozilla.org/zh-CN/docs/Web/HTTP/Basics_of_HTTP/MIME_types/Common_types
       * */
      const typeList = ['audio/mpeg', 'audio/wav']
      if (!typeList.includes(file.type)) {
        this.$message.warning('请上传正确的音频文件')
        return false
      }
      /** 校验文件尺寸*/
      // 文件容量（M）
      const maxSize = 10
      const isOverflow = file.size / 1024 / 1024 > maxSize
      if (isOverflow) {
        this.$message.warning(`文件大小不能超过${maxSize}M`)
        return false
      }

      return true
    },
    /**
     * 上传音频
     * */
    uploadAudio(file) {
      this.closeAudio?.()
      const validate = this.fileVerification(file.raw)
      if (!validate) return
      const rawFile = file.raw
      const reader = new FileReader()
      reader.readAsDataURL(rawFile)
      reader.onload = (e) => {
        this.uploading = true
        uploadAudio({ audioUrl: e.target.result, audioFileName: rawFile.name })
          .then((res) => {
            if (res.code === 200) {
              this.data.audioUrl = res.data.audioUrl
              this.data.audioFileName = res.data.audioFileName
            } else {
              this.$message.error('上传失败')
            }
          })
          .catch(() => {
            this.$message.error('上传失败')
          })
          .finally(() => {
            this.uploading = false
          })
      }
    },
    /**
     * 保存数据
     * */
    submit() {
      this.closeAudio?.()
      this.submitLoading = true
      setTactics(this.data)
        .then((res) => {
          if (res.code === 200) {
            this.$message.success('保存成功')
            this.doQuery()
          }
        })
        .finally(() => {
          this.submitLoading = false
        })
    },
    /**
     * 试听
     * */
    playAudio() {
      if (!this.data.audioUrl || this.disabled) return
      this.closeAudio?.()
      this.closeAudio = playAudio(this.data.audioUrl, this.data.cycleCount)
    },
  },
}
</script>

<style scoped lang="scss">
.top-title {
  display: flex;
  align-items: center;
  margin-top: 28px;
  color: #202225;
  font-size: 18px;
  .title-label {
    width: 4px;
    height: 18px;
    background: #1768eb;
    margin-right: 11px;
  }
}

.form-box {
  padding: 21px 20px;
  margin-top: 25px;
  width: 100%;
  height: 140px;
  background: #ffffff;
  border-radius: 4px;
  border: 1px solid #e0e0e0;
  display: flex;
  flex-direction: column;
  justify-content: center;
  gap: 20px 0;
  .item-row {
    display: flex;
    align-items: center;
    .label-item {
      white-space: pre;
      display: inline-block;
      margin-left: 10px;
      margin-right: 10px;
    }
  }
}

.audio-table {
  width: 100%;
  border-collapse: collapse;
  border-radius: 4px;
  font-size: 15px;
  tr {
    height: 6vh;
    text-align: center;
  }
  thead {
    background-color: #f7f8f9;
    th {
      font-weight: 400;
    }
  }
  tbody {
    td {
      width: 25%;
    }
    .disabled {
      color: #afb1b9;
      filter: grayscale(1);
      //cursor: not-allowed;
    }
  }
  td,
  th {
    border: 1px solid #e0e0e0;
  }
}

.audio-operate {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 0 97px;
  user-select: none;
  .item {
    display: flex;
    align-items: center;
    gap: 0 9px;
    cursor: pointer;
  }

  .icon {
    width: 22px;
    //height: 17px;
  }
  .text {
    color: #1768eb;
    text-decoration: underline;
  }
}

.footer {
  display: flex;
  justify-content: center;
  margin-top: 7vh;
}

.level-item {
  display: flex;
  gap: 0 10px;
  justify-content: center;
  align-items: center;
}

.level-box {
  display: flex;
  flex-direction: column;
  gap: 10px 0;
}

.container {
  height: calc(100vh - 250px);
  overflow-y: auto;
}
</style>
