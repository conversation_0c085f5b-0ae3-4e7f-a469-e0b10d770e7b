#app {

  .main-container {
    height: calc(100%) !important;
    transition: margin-left .28s;
    margin-left: 150px !important;
    position: relative;
    padding: 90px 20px 20px 0px;
    box-sizing: border-box;
    background-color: #F4F7FE;
    // margin-top: 90px;
  }


  .sidebar-container {
    transition: width 0.28s;
    width: 150px !important;
    background-color: #fff;
    height: 100%;
    position: fixed;
    font-size: 0px;
    top: 0;
    bottom: 0;
    left: 0;
    z-index: 1;
    overflow: hidden;

    // reset element-ui css
    .horizontal-collapse-transition {
      transition: 0s width ease-in-out, 0s padding-left ease-in-out, 0s padding-right ease-in-out;
    }

    .scrollbar-wrapper {
      overflow-x: hidden !important;
    }

    .el-scrollbar__bar.is-vertical {
      right: 0px;
    }

    .el-scrollbar {
      height: 100%;
    }

    &.has-logo {
      .el-scrollbar {
        height: calc(100% - 50px);
      }
    }

    .is-horizontal {
      display: none;
    }

    a {
      display: inline-block;
      width: 100%;
      overflow: hidden;
    }

    .svg-icon {
      margin-right: 16px;
    }

    .sub-el-icon {
      margin-right: 12px;
      margin-left: -2px;
    }

    .el-menu {
      border: none;
      height: 100%;
      width: 100% !important;
    }

    // menu hover
    .submenu-title-noDropdown,
    .el-submenu__title {
      font-weight: 400 !important;
      // margin-left: 20px !important;
      // color: #40424E !important;

      &:hover {
        // color: #1071E2 !important;
        // background-color: #fff !important;
        box-shadow: 0px 5px 20px 1px rgba(144, 171, 212, 0.08) !important;
        // border-radius: 16px 0px 0px 16px !important;
        // margin-left: 20px !important;
      }
    }

    .is-active {
      font-weight: bold !important;
      .el-submenu__icon-arrow {
        // 选中^ 符号颜色
        color: #1071E2 !important;
      }

      >.el-submenu__title {
        // color: $subMenuActiveText !important;
        color: #1071E2 !important;
        font-weight: bold !important;
        background: #FFFFFF !important;
        box-shadow: 0px 5px 20px 1px rgba(144, 171, 212, 0.08) !important;
        border-radius: 16px 0px 0px 16px !important;
        margin-left: 20px !important;

      }
    }

    & .nest-menu .el-submenu>.el-submenu__title,
    & .el-submenu .el-menu-item {
      font-weight: 400 !important;
      margin-left: 40px !important;

      &:hover {
        color: #1071E2 !important;
        // background-color: #fff !important;
        background: transparent !important;
        // box-shadow: 0px 5px 20px 1px rgba(144, 171, 212, 0.08) !important;
        // border-radius: 16px 0px 0px 16px !important;
        margin-left: 40px !important;
      }

      &.is-active {
        // width: 239px;
        // height: 50px;
        // color: #1071E2 !important;
        // font-size: 18px;
        // font-family: PingFang SC, PingFang SC;
        font-weight: bold !important;
        // font-size: 18px !important;
        font-family: PingFang SC, PingFang SC;
        color: #1071E2 !important;
      }
    }

    .el-menu-item .is-active {
      // 一级菜单的选中样式修改
      // width: 239px;
      // height: 50px;
      // color: #fff !important;
      color: #1071E2 !important;
      font-weight: bold !important;

      // background: $subMenuHover !important;
      // box-shadow: 0px 5px 6px 0px rgba(36, 91, 219, 0.17);
      // border-radius: 0px 25px 25px 0px;
    }

    .el-submenu .el-menu-item {
      //  子菜单
      color: #727681 !important;
      font-weight: 400 !important;
    }
  }

  .hideSidebar {
    .sidebar-container {
      width: 150px !important;
    }

    .main-container {
      margin-left: 150px !important;
    }

    .submenu-title-noDropdown {
      padding: 0 !important;
      position: relative;

      .el-tooltip {
        padding: 0 !important;

        .el-image {
          margin-left: 18px;
        }

        .svg-icon {
          margin-left: 20px;
        }

        .sub-el-icon {
          margin-left: 19px;
        }
      }
    }

    .el-submenu {
      overflow: hidden;

      &>.el-submenu__title {
        padding: 0 !important;

        .svg-icon {
          margin-left: 20px;
        }

        .el-image {
          margin-left: 20px;
        }

        .sub-el-icon {
          margin-left: 19px;
        }

        .el-submenu__icon-arrow {
          display: none;
        }
      }

    }

    .el-menu--collapse {
      .el-submenu {
        &>.el-submenu__title {
          &>span {
            height: 0;
            width: 0;
            overflow: hidden;
            visibility: hidden;
            display: inline-block;
          }
        }
      }
    }
  }

  .el-menu--collapse .el-menu .el-submenu {
    min-width: $sideBarWidth !important;
  }

  // mobile responsive
  .mobile {
    .main-container {
      margin-left: 150px !important;
    }

    .sidebar-container {
      transition: transform .28s;
      width: 150px !important;
    }

    &.hideSidebar {
      .sidebar-container {
        // pointer-events: none;
        // transition-duration: 0.3s;
        // transform: translate3d(-$sideBarWidth, 0, 0);
        width: 150px !important;
      }
    }
  }

  .withoutAnimation {

    .main-container,
    .sidebar-container {
      transition: none;
    }
  }
}

// when menu collapsed
.el-menu--vertical {
  &>.el-menu {
    .svg-icon {
      margin-right: 16px;
    }

    .sub-el-icon {
      margin-right: 12px;
      margin-left: -2px;
    }
  }

  .nest-menu .el-submenu>.el-submenu__title,
  .el-menu-item {
    // opacity: 0.25;
    opacity: 1;
    color: #fff;

    &.is-active {
      background-color: $menuHover !important;
      font-weight: bold !important;
    }

    &:hover {
      // you can use $subMenuHover
      background-color: $menuHover !important;
      // width: 239px;
      // height: 50px;
      // box-shadow: 0px 5px 6px 0px rgba(36, 91, 219, 0.17);
      // border-radius: 0px 25px 25px 0px;
    }
  }

  // the scroll bar appears when the subMenu is too long
  >.el-menu--popup {
    max-height: 100vh;
    overflow-y: auto;

    &::-webkit-scrollbar-track-piece {
      background: #d3dce6;
    }

    &::-webkit-scrollbar {
      width: 6px;
    }

    &::-webkit-scrollbar-thumb {
      background: #99a9bf;
      border-radius: 20px;
    }
  }
}
