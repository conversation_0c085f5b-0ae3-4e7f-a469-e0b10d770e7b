<template>
  <div
    :id="id"
    :style="{ height: height, width: width }"
  />
</template>

<script>
import elementResizeDetectorMaker from 'element-resize-detector'
import chartMixins from './mixins/index'

export default {
  name: 'Dashboard',
  mixins: [chartMixins],
  props: {
    id: {
      require: true,
      type: String,
      default: 'charts'
    },
    width: {
      require: false,
      type: String,
      default: '100%'
    },
    height: {
      require: false,
      type: String,
      default: '100%'
    },
    propData: {
      require: false,
      type: Object,
      default: () => {}
    }
  },
  watch: {
  },
  mounted() {
    this.$nextTick(() => {
      this.initChart()
    })
    const erd = elementResizeDetectorMaker()
    erd.listenTo(document.getElementById(this.id), (element) => {
      const width = element.offsetWidth
      const height = element.offsetHeight
      this.$nextTick(() => {
        console.log(`Size: ${width}x${height}`)
        // 使echarts尺寸重置
        this.chart = this.$echarts.init(document.getElementById(this.id)).resize()
      })
    })
  },
  methods: {
    initChart() {
      this.chart = this.$echarts.init(document.getElementById(this.id))
      this.option = {
        tooltip: {
          formatter: '{a} <br/>{b} : {c}'
        },
        title: [
          {
            text: `${this.propData.outputValue}亿元`,
            left: '49%',
            bottom: '7%',
            textAlign: 'center',
            textStyle: {
              color: '#333',
              fontWeight: '500',
              fontSize: 20
            }
          }],
        series: [
          {
            type: 'gauge',
            name: '企业数量',
            progress: {
              show: true,
              width: 8
            },
            max: 5500, // 表盘最大值
            // itemStyle: {
            //   normal: {
            //     color: '#4694EC'
            //   }
            // },
            axisLine: {
              lineStyle: {
                width: 8
              }
            },
            axisTick: {
              show: false
            },
            splitLine: { // 表盘刻度
              length: 6,
              lineStyle: {
                width: 1,
                color: '#999'
              }
            },
            splitNumber: 5, // 表盘刻度数量
            axisLabel: { // 表盘刻度字
              distance: 16,
              color: '#999',
              fontSize: 9
            },
            anchor: { // 表盘指针
              show: true,
              showAbove: true,
              size: 22,
              itemStyle: {
                borderWidth: 3
              }
            },
            title: {
              // show: false
              show: true,
              offsetCenter: [0, '65%'],
              textStyle: {
                fontSize: 14,
                fontWeight: '400',
                color: '#808080'
              },
              text: '11111111111111',
              subtext: '{name}'
            },
            detail: { // 字体
              // formatter: '{value}',
              show: false,
              valueAnimation: true,
              // fontSize: 26,
              textStyle: {
                fontSize: 26,
                fontWeight: '500',
                color: '#333'
              },
              offsetCenter: [0, '95%']
            },
            data: [
              // {
              //   value: 70,
              //   name: '测试name'
              // }
              this.propData
            ]
          }
        ]
      }
      this.chart.setOption(this.option, true)
    }
  }
}
</script>

<style>

</style>

