<template>
  <div
    class="pop_item "
    :class="isAlarm ? 'alarm' : 'normal'"
  >
    <div class="number">{{ data.name.split('-')[1] }}</div>
    <div class="name">{{ data.name }}</div>
    <div
      v-if="temperature"
      class="temperature"
    >{{ temperature.value / 100 }}℃ </div>
    <div
      v-if="isAlarm"
      class="wave-container-pop"
      style="left: -6px"
    >
      <div class="wave wave1 color_alarm" />
      <div class="wave wave2 color_alarm" />
      <div class="wave wave3 color_alarm" />
    </div>
  </div>
</template>

<script>
export default {
  name: 'Popup',
  props: {
    threshold: {
      type: Number,
      default: 40
    },
    data: Object
  },
  computed: {
    temperature() {
      return this.data.temperature
    },
    isAlarm() {
      return this.data.temperature && this.data.temperature.value > this.threshold
    }
  }
}

</script>
<style scoped lang="scss">
.pop_item {
  position: absolute;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  width: 75px;
  height: 80px;
  font-size: 11px;
  // font-weight: bold;
  line-height: 17px;
  z-index: 1;
  cursor: pointer;
  .alarm_water {
    position: absolute;
    bottom: -15px;
    left: 0;
    width: 100%;
    height: 50%;
  }
  .alarm_kuang {
    position: absolute;
    top: -39px;
    left: -13px;
    width: 122px;
    height: 205px;
  }
  .alarm_kuang1 {
    position: absolute;
    top: -48px;
    left: -18px;
    width: 140px;
    height: 258px;
  }
  .pop_kuang {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
  }
  .number {
    color: #C4DDFF;
  }
  .name {
    color: #C4DDFF;
  }
  .temperature {
    font-weight: bold;
    color: #38EA08;
  }
}

.cab_alarm {
  position: absolute;
}
.alarm {
  background: url(~@/assets/page/kuang3.png) no-repeat;
  background-size: 100% 100%;
  .temperature {
    color: #FF433D;
  }
}
.normal {
  background: url(~@/assets/page/kuang5.png) no-repeat;
  background-size: 100% 100%;
}

.wave-container-pop {
  position: absolute;
  bottom: -40px;
  left: 4px;
  width: 90px;
  height: 80px;
  .wave {
    position: absolute;
    bottom: 50%;
    left: 50%;
    width: 0;
    height: 0;
    border-radius: 50%;
    transform: rotateX(70deg);
    animation: wave-pop-animation 3s linear infinite;
  }
  .color_alarm {
    background: red;
  }
  .color_normal {
    background: #0e82ff;
  }
  .wave1 {
    animation-delay: -1s;
  }
  .wave2 {
    animation-delay: -2s;
  }
  .wave3 {
    animation-delay: -3s;
  }
}
@keyframes wave-pop-animation {
  100% {
    width: 90px;
    height: 80px;
    opacity: 0;
    bottom: 0;
    left: 0;
  }
}
</style>
