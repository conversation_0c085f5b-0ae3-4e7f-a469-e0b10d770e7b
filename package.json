{"name": "vue-admin-template", "version": "4.4.0", "description": "A vue admin template with Element UI & axios & iconfont & permission control & lint", "author": "Pan <<EMAIL>>", "scripts": {"dev": "vue-cli-service serve", "build:prod": "vue-cli-service build", "build:stage": "vue-cli-service build --mode staging", "preview": "node build/index.js --preview", "svgo": "svgo -f src/icons/svg --config=src/icons/svgo.yml", "lint": "eslint --ext .js,.vue src", "eslint": "eslint --ext .js,.vue src --fix", "test:unit": "jest --clearCache && vue-cli-service test:unit", "test:ci": "npm run lint && npm run test:unit"}, "dependencies": {"@geoman-io/leaflet-geoman-free": "^2.17.0", "axios": "0.18.1", "copy-webpack-plugin": "^4.6.0", "core-js": "3.6.5", "dayjs": "^1.11.10", "echarts": "^5.1.2", "element-resize-detector": "1.2.3", "element-ui": "2.15.14", "eslint-plugin-vuejs-accessibility": "^1.1.1", "gsap": "^3.12.5", "js-cookie": "2.2.0", "leaflet": "^1.9.4", "normalize.css": "7.0.0", "nprogress": "0.2.0", "path-to-regexp": "2.4.0", "postcss-px-to-viewport": "^1.1.1", "screenfull": "^4.2.1", "spark-md5": "^3.0.1", "three": "^0.179.1", "vue": "2.6.10", "vue-baidu-map": "0.21.22", "vue-dompurify-html": "^2.3.0", "vue-router": "3.0.6", "vue-virtual-scroll-list": "^2.3.5", "vuex": "3.1.0"}, "devDependencies": {"@vue/cli-plugin-babel": "4.5.19", "@vue/cli-plugin-eslint": "4.4.4", "@vue/cli-plugin-unit-jest": "4.4.4", "@vue/cli-service": "4.4.4", "@vue/eslint-config-airbnb": "^5.3.0", "@vue/test-utils": "1.0.0-beta.29", "autoprefixer": "9.5.1", "babel-eslint": "10.1.0", "babel-jest": "23.6.0", "babel-plugin-dynamic-import-node": "2.3.3", "chalk": "2.4.2", "connect": "3.6.6", "eslint": "^6.7.2", "eslint-import-resolver-webpack": "^0.13.1", "eslint-plugin-import": "^2.23.4", "eslint-plugin-vue": "^6.2.2", "html-webpack-plugin": "3.2.0", "husky": "^6.0.0", "lint-staged": "^11.2.6", "mockjs": "1.0.1-beta3", "postcss-pxtorem": "^5.1.1", "runjs": "4.3.2", "sass": "1.26.8", "sass-loader": "8.0.2", "script-ext-html-webpack-plugin": "2.1.3", "serve-static": "1.13.2", "svg-sprite-loader": "4.1.3", "svgo": "1.2.2", "vue-template-compiler": "2.6.10"}, "browserslist": ["> 1%", "last 2 versions"], "engines": {"node": ">=8.9", "npm": ">= 3.0.0"}, "husky": {"hooks": {"pre-commit": "lint-staged"}}, "lint-staged": {"*.{js,vue}": ["vue-cli-service lint", "git add"]}, "license": "MIT"}