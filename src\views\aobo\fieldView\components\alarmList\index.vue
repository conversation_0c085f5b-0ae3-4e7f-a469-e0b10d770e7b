<template>
  <div v-show="isAlarm">
    <div
      v-if="!showAlarm"
      class="alarm_icon"
      @click="showAlarm = true"
    >
      <img
        src="@/assets/page/alarm.png"
      >
      <div
        class="wave-container-alarm"
      >
        <div class="wave wave1" />
        <div class="wave wave2" />
        <div class="wave wave3" />
      </div>
    </div>

    <div
      v-else
      class="right"
      :style="{marginTop: isFullscreen ? '80px' : '0'}"
    >
      <div
        class="alarm_list"
      >
        <img
          class="close_icon"
          src="@/assets/page/<EMAIL>"
          @click="showAlarm = false"
        >
        <div
          class="title"
        >
          <img src="@/assets/page/alarm.png">
          <div>告警列表</div>
        </div>
        <el-radio-group
          v-model="dataType" size="small"
          style="margin-left: 20px;"
        >
          <el-radio-button label="1">温度报警({{ tempAlarmCount }})</el-radio-button>
          <el-radio-button label="2">位移报警({{ sensorAlarmCount }})</el-radio-button>
        </el-radio-group>

        <TempAlarmList
          v-show="dataType === '1'"
          ref="tampAlarmRef"
          :btn-authority-list="btnAuthorityList"
          :serial-num="serialNum"
          :time-interval="timeInterval"
          @detail="$emit('detail', $event)"
          @isAlarm="tempAlarm"
        />
        <SensorAlarmList
          v-show="dataType === '2'"
          ref="sensorAlarmRef"
          :btn-authority-list="btnAuthorityList"
          :work-face-id="workFaceId"
          :time-interval="timeInterval"
          @detail="$emit('sensorDetail', $event)"
          @isAlarm="sensorAlarm"
        />
      </div>
    </div>
  </div>
</template>

<script>
import TempAlarmList from '@/views/aobo/fieldView/components/alarmList/TempAlarmList'
import SensorAlarmList from '@/views/aobo/fieldView/components/alarmList/SensorAlarmList'

export default {
  name: 'AlarmBattery',
  components: { TempAlarmList, SensorAlarmList },
  props: {
    timeInterval: {
      type: Number,
      default: 30
    },
    btnAuthorityList: {
      type: Array,
      default() {
        return []
      }
    },
    serialNum: {
      type: String,
      default: ''
    },
    isFullscreen: {
      type: Boolean,
      default: false
    },
    workFaceId: {
      type: Number,
      default: 0
    }
  },
  data() {
    return {
      showAlarm: true,
      dataType: '1',
      hasTempAlarm: false,
      tempAlarmCount: 0,
      hasSensorAlarm: false,
      sensorAlarmCount: 0
    }
  },
  computed: {
    isAlarm() {
      return this.hasTempAlarm || this.hasSensorAlarm
    }
  },
  methods: {
    tempAlarm(val, total) {
      this.tempAlarmCount = total
      this.hasTempAlarm = val
      if (val) {
        if (!this.hasSensorAlarm) {
          this.dataType = '1'
        }
      } else {
        this.dataType = '2'
      }
    },
    sensorAlarm(val, total) {
      this.sensorAlarmCount = total
      this.hasSensorAlarm = val
      if (val) {
        if (!this.hasTempAlarm) {
          this.dataType = '2'
        }
      } else {
        this.dataType = '1'
      }
    },
    doPage(type) {
      this.dataType = type
      if (type === '1') {
        this.$refs.tampAlarmRef.doPage(true)
      } else {
        this.$refs.sensorAlarmRef.doPage(true)
      }
    },
  }
}
</script>

<style scoped lang="scss">
.right {
  position: absolute;
  right: 30px;
  top: 20px;
  bottom: 20px;
  width: 360px;
  z-index: 998 !important;
  .alarm_list {
    width: 100%;
    height: 100%;
    background: url(~@/assets/page/kuang6.png) no-repeat;
    background-size: 100% 100%;
    .close_icon {
      width: 15px;
      height: 15px;
      position: absolute;
      right: 25px;
      top: 13px;
      cursor: pointer;
    }
    .title {
      width: 100%;
      height: 50px;
      display: flex;
      justify-content: center;
      align-items: center;
      img {
        width: 25px;
        height: 25px;
        margin-right: 7px;
      }
      div {
        font-size: 20px;
        font-weight: bold;
        background: linear-gradient(0deg, #FF443A 0%, #FFDB11 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
      }
    }
    .table_list {
      padding: 10px 20px;
      height: 100%;
      overflow: hidden;
      .list_item {
        padding: 0 15px;
        width: 320px;
        height: 155px;
        background: rgba(27,95,225,0.3);
        border-radius: 8px;
        margin-bottom: 15px;
        // font-weight: bold;
        cursor: pointer;
        .list_item_title {
          height: 40px;
          line-height: 40px;
          margin-bottom: 5px;
          border-bottom: 1px solid rgba(142, 216, 246, 0.3);
          display: flex;
          justify-content: space-between;
          font-size: 15px;
          font-weight: bold;
          .name {
            color: #fff;
          }
          .temperature {
            color: #FF511C;
          }
        }
        .list_item_detail {
          font-size: 13px;
          color: #C4DDFF;
          display: flex;
          flex-wrap: wrap;
          justify-content: space-between;
          line-height: 30px;
          margin-bottom: 5px;
          div {
            &:nth-child(n){
              width: 58%;
            }
            &:nth-child(2n){
              width: 40%;
            }
            text-overflow: ellipsis;
            overflow: hidden;
            white-space: nowrap;
          }
        }
        .list_item_bottom {
          display: flex;
          align-items: center;
          justify-content: space-between;
          .time {
            display: flex;
            align-items: center;
            img {
              width: 15px;
              height: 15px;
              margin-right: 10px;
            }
            div {
              color: #91A0B4;
              font-size: 14px;
              font-weight: bold;
              display: flex;
            }
          }
          img {
            width: 20px;
            height: 28px;
            cursor: pointer;
          }
        }
      }
    }
  }
}

.alarm_icon {
  position: absolute;
  right: 90px;
  top: 50px;
  cursor: pointer;
  z-index: 1000;
  img {
    position: absolute;
    width: 50px;
    height: 50px;
    z-index: 2;
  }
}

.wave-container-alarm {
  position: absolute;
  top: -39px;
  left: -35px;
  width: 120px;
  height: 120px;
  z-index: 1;
  .wave {
    position: absolute;
    bottom: 50%;
    left: 50%;
    width: 0;
    height: 0;
    border-radius: 50%;
    animation: wave-alarm-animation 3s linear infinite;
    background: red;
  }
  .wave1 {
    animation-delay: -1s;
  }
  .wave2 {
    animation-delay: -2s;
  }
  .wave3 {
    animation-delay: -3s;
  }
}
@keyframes wave-alarm-animation {
  100% {
    width: 120px;
    height: 120px;
    opacity: 0;
    bottom: 0;
    left: 0;
  }
}

</style>
