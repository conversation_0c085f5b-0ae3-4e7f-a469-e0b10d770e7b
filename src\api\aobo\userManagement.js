import request from '@/utils/request'

export function doPage(data) {
  return request({
    url: '/api/v1/sys/account/doPage',
    method: 'post',
    data
  })
}

/**
 * 下拉数据
 * */
export function dropdownList() {
  return request({
    url: '/api/v1/sys/account/doDropPerson',
    method: 'get'
  })
}

export function resetPassword(data) {
  return request({
    url: `/api/v1/sys/account/resetPassword`,
    method: 'post',
    data
  })
}

export function add(data) {
  return request({
    url: `/api/v1/sys/account/add`,
    method: 'post',
    data
  })
}

export function update(data) {
  return request({
    url: `/api/v1/sys/account/update`,
    method: 'post',
    data
  })
}

export function enableOrDisable(id) {
  return request({
    url: `/api/v1/sys/account/enableOrDisable?id=${id}`,
    method: 'get'
  })
}

export function del(id) {
  return request({
    url: `/api/v1/sys/account/del?id=${id}`,
    method: 'get'
  })
}

export function getRoleList() {
  return request({
    url: `/api/v1/sys/role/roleList`,
    method: 'get'
  })
}

// 下载模板
export function download(data) {
  return request({
    url: '/api/v1/sys/account/download',
    method: 'get',
    data,
    responseType: 'blob'
  })
}

// 导入
export function importExcel(data) {
  return request({
    url: `/api/v1/sys/account/importExcel`,
    method: 'post',
    data
  })
}

// 导出
export function bacthExport(data) {
  return request({
    url: '/api/v1/sys/account/export',
    method: 'get',
    data,
    responseType: 'blob'
  })
}
