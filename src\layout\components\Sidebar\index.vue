<template>
  <div
    class="menu"
    :class="{'has-logo':showLogo}"
  >
    <logo
      v-if="showLogo"
      :collapse="isCollapse"
    />
    <!-- <hamburger
      :is-active="sidebar.opened"
      class="hamburger-container"
      @toggleClick="toggleSideBar"
    /> -->
    <!-- <el-scrollbar wrap-class="scrollbar-wrapper">
      <el-menu
        :default-active="activeMenu"
        :collapse="isCollapse"
        :background-color="variables.menuBg"
        :text-color="variables.menuText"
        :unique-opened="true"
        :router="true"
        :active-text-color="variables.menuActiveText"
        :collapse-transition="true"
        mode="vertical"
        @select="select"
      >
        <sidebar-item
          v-for="route in routes"
          :key="route.path"
          :item="route"
          :base-path="route.path"
        />
      </el-menu>
    </el-scrollbar> -->
    <el-menu
      :default-active="activeMenu"
      :collapse="isCollapse"
      :background-color="variables.menuBg"
      :text-color="variables.menuText"
      :unique-opened="true"
      :router="true"
      :active-text-color="variables.menuActiveText"
      :collapse-transition="true"
      mode="vertical"
      @select="select"
    >
      <sidebar-item
        v-for="route in routes"
        :key="route.path"
        :item="route"
        :base-path="route.path"
      />
    </el-menu>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import variables from '@/styles/variables.scss'
import store from '@/store'
// import Hamburger from '@/components/Hamburger'
import Logo from './Logo'
import SidebarItem from './SidebarItem'

export default {
  components: { SidebarItem, Logo },
  computed: {
    ...mapGetters([
      'sidebar',
      'addRoutes'
    ]),
    routes() {
      return this.$router.options.routes.concat(store.getters.addRoutes)
    },
    activeMenu() {
      const route = this.$route
      const { meta, path } = route
      // if set path, the sidebar will highlight the path you set
      if (meta.activeMenu) {
        return meta.activeMenu
      }
      // 初始化改变选中的一级菜单图标
      this.$store.getters.addRoutes.forEach((item) => {
        // 排除首页路由‘/’的影响
        if (path.includes(item.path) && item.path.length > 1) {
          item.meta.iconImage = item.meta.iconActive
          this.$set(item.meta, 'isActive', true)
          // item.meta.iconImage = item.meta.iconDefault
        } else {
          item.meta.iconImage = item.meta.iconDefault
          this.$set(item.meta, 'isActive', false)
        }
      })
      return path
    },
    showLogo() {
      return this.$store.state.settings.sidebarLogo
    },
    variables() {
      return variables
    },
    isCollapse() {
      return !this.sidebar.opened
    }
  },
  methods: {
    toggleSideBar() {
      this.$store.dispatch('app/toggleSideBar')
    },
    /**
     * 选中菜单，更改一级菜单图标
     */
    select(index, indexPath) {
      this.$store.getters.addRoutes.forEach((element) => {
        if (element.path === indexPath[0]) {
          element.meta.iconImage = element.meta.iconActive
          this.$set(element.meta, 'isActive', true)
          // element.meta.iconImage = element.meta.iconDefault
        } else {
          element.meta.iconImage = element.meta.iconDefault
          this.$set(element.meta, 'isActive', false)
        }
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.menu{
  display: flex;
  flex-direction: column;
}
.hamburger-container {
    line-height: 40px;
    margin-left: 7px;
    color: #999999;
    float: left;
    cursor: pointer;
    transition: background .3s;
    -webkit-tap-highlight-color:transparent;

    &:hover {
      background: rgba(0, 0, 0, .025)
    }
  }
</style>
