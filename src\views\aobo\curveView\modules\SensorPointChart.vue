<template>
  <div style="position:relative;">
    <div
      id="lineCharts"
      style="height: calc(100vh - 9.5rem); width: 100%;"
    />
  </div>
</template>
<script>
import * as echarts from 'echarts'

const alarmColorMap = {
  0: '#0000FF',
  1: '#FFA500',
  2: '#e4e46f',
  3: '#FF0000',
}

export default {
  name: 'LineCharts',
  props: {
    propData: {
      require: false,
      type: Object,
      default: () => {}
    },
    page: {
      type: String,
      default: 'first'
    },
    alarmLevel: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      timer: null,
      total: 10000
    }
  },
  created() {
  },
  mounted() {
    window.addEventListener('resize', this.onResize)
    this.$nextTick(() => {
      this.initcharts()
    })
  },
  destroyed() {
    window.removeEventListener('resize', this.onResize)
  },

  methods: {
    initcharts() {
      this.chart = echarts.init(document.getElementById('lineCharts'))
    },
    updateOption(xData, yData) {
      this.chart.clear()
      const option = {
        color: ['#5470c6', '#91cc75', '#fac858', '#ee6666', '#73c0de', '#3ba272', '#fc8452', '#9a60b4', '#ea7ccc'],
        legend: {
          type: 'plain',
          top: 0,
          right: 50,
          itemGap: 50,
          itemWidth: 20,
          itemHeight: 7,
          icon: 'roundRect'
        },
        grid: {
          left: 80,
          top: 40,
          bottom: 10,
          right: 110,
          containLabel: true
        },
        dataZoom: [{
          // filterMode: 'none',
          type: 'inside',
          // start: this.zoomStartChild,
          start: 99,
          end: 100,
          maxSpan: 1
        }],
        xAxis: {
          name: '时间',
          type: 'category',
          data: xData,
          nameTextStyle: {
            color: '#9FA3AB',
            fontSize: 14
          },
          axisLine: {
            lineStyle: {
              color: '#8F98A0'
            }
          },
          axisTick: { length: 0 },
          axisLabel: {
            textStyle: {
              color: '#909090'
            },
            fontSize: 14
            // formatter: (value) => `${value.split(' ')[1]}`
          },
          splitLine: {
            show: false,
            lineStyle: {
              type: 'dashed'
            }
          }
        },
        yAxis: {
          name: '单位：mm',
          type: 'value',
          axisLine: { show: false },
          axisLabel: {
            color: '#9FA3AB'
          },
          nameTextStyle: {
            color: '#9FA3AB',
            fontSize: 14,
            align: 'center',
            padding: [0, 0, 5, 0]
          },
          splitLine: {
            show: true,
            lineStyle: {
              type: 'dashed'
            }
          },
          splitNumber: 10
        },
        tooltip: {
          trigger: 'axis',
          enterable: true,
          padding: [12, 15, 20, 20],
          textStyle: { color: '#424242' },
          renderMode: 'html',
          formatter: '{b}<br />{c0}mm',
          className: 'tooltip'
        },
        series: [{
          // name: el.name,
          type: 'line',
          data: yData,
          smooth: true, // 平滑曲线
          symbolSize: 8,
          showAllSymbol: false,
          sampling: 'lttb',
          // sampling: 'average',
          symbol: 'none',
          lineStyle: { width: 2.5 },
          label: {
            show: false,
            position: 'top',
          }
        }]
      }
      // 添加阈值线，正负两条
      const alarmLevel = this.alarmLevel.map((item) => [item, { ...item, value: -item.value }]).flat()
      for (const item of alarmLevel) {
        option.series.push({
          name: '',
          type: 'line',
          data: [item.value],
          symbol: 'none',
          label: {
            show: false
          },
          markLine: {
            silent: true,
            data: [{
              name: `报警线：${item.value}mm`,
              yAxis: item.value,
              label: {
                formatter: '{b}',
                position: 'end',
                color: alarmColorMap[item.level],
              }
            }],
            lineStyle: {
              color: alarmColorMap[item.level],
              width: 2
            },
            symbol: 'none',
            label: {
              distance: [0, 8]
            }
          }
        })
      }
      this.chart.setOption(option)
    },
    onResize() {
      this.chart.resize()
    }

  }
}
</script>
<style lang="scss" scoped>
.remark {
  position: absolute;
  top: 0;
  right: 20px;
  display: flex;
  justify-content: space-between;
  width: 180px;
  height: 20px;
  // line-height: 50px;
  .remark_item {
    display: flex;
    align-items: center;
    .dot {
      // width: 21px;
      // height: 6px;
      // margin-right: 5px;
      // border-radius: 2px;
      width: 10px;
      height: 10px;
      margin-right: 5px;
      border-radius: 10px;
    }
    .dot1 {
      background-color: #00E667;
    }
    .dot2 {
      background-color: #F95F5F;
    }
    .text {
      color: black;
      font-family: PingFang SC RE;
      // font-weight: bold;
      font-size: 14px;
    }
  }
}
</style>
