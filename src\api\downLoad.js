import request from '@/utils/request'

// 获取楼宇信息列表
export function getList({ pageNum, pageSize }) {
  return request({
    url: `${'/api/v1/async/list?pageNum='}${pageNum}&pageSize=${pageSize}`,
    method: 'get'
  })
}
// 获取文件
export function getFile({ filename, rename }) {
  console.log(filename)
  return request({
    url: `/api/v1/file?filename=${filename}&rename=${rename}`,
    method: 'get',
    responseType: 'blob'
  })
}
// 任务重复数据覆盖
export function coverSure(data) {
  return request({
    url: `/api/v1/sys-async-task-repeat/cover`,
    method: 'put',
    data
  })
}
