import { asyncRoutes, constantRoutes } from '@/router'
/**
 * Use meta.role to determine if the current user has permission使用元。角色确定当前用户是否具有权限
 * @param menuList
 * @param route
 */
function hasPermission(menuList, route) {
  if (route.meta && route.meta.code) {
    return menuList.includes(route.meta.code)
  }
  return true
}
/**
 * Filter asynchronous routing tables by recursion 通过递归过滤异步路由表
 * @param routes asyncRoutes  异步的路线
 * @param menuList
 */
export function filterAsyncRoutes(routes, menuList) {
  const res = []
  routes.forEach((route) => {
    const tmp = { ...route }
    // todo：删除||true
    if (hasPermission(menuList, tmp)) {
      if (tmp.children) {
        tmp.children = filterAsyncRoutes(tmp.children, menuList)
      }
      res.push(tmp)
    }
  })

  return res
}

const state = {
  hasFilterAsyncRoutes: false,
  addRoutes: []
}

const mutations = {
  SET_ROUTES: (state, routes) => {
    state.addRoutes = routes
    state.routes = constantRoutes.concat(routes)
  },
  HAS_FILTER_ROUTES: (state, hasFilterAsyncRoutes) => {
    state.hasFilterAsyncRoutes = hasFilterAsyncRoutes
  }
}

const actions = {
  generateRoutes({ commit }, menuList) {
    return new Promise((resolve) => {
      const accessedRoutes = filterAsyncRoutes(asyncRoutes, menuList)
      commit('SET_ROUTES', accessedRoutes)
      commit('HAS_FILTER_ROUTES', true)
      resolve(accessedRoutes)
    })
  }
  // generateRoutes({ commit }) {
  //   return new Promise((resolve) => {
  //     const accessedRoutes = [...asyncRoutes]
  //     commit('SET_ROUTES', accessedRoutes)
  //     commit('HAS_FILTER_ROUTES', true)
  //     resolve(accessedRoutes)
  //   })
  // }
}

export default {
  namespaced: true,
  state,
  mutations,
  actions
}
