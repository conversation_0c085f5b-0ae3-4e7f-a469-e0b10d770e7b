<template>
  <div
    :id="id"
    :style="{ height: height, width: width }"
  />
</template>
<script>
import chartsMixIn from './mixins'

export default {
  name: '',
  mixins: [chartsMixIn],
  props: {
    id: {
      require: false,
      type: String,
      default: 'charts'
    },
    width: {
      require: false,
      type: String,
      default: '100%'
    },
    height: {
      require: false,
      type: String,
      default: '100%'
    },
    propData: {
      require: false,
      type: Object,
      default: () => {}
    }
  },
  created() {
    this.doNotRedraw = true
  },
  mounted() {
    this.initChart()
    console.log(this.propData, 888888888)
  },
  methods: {
    initChart() {
      this.chart = this.$echarts.init(document.getElementById(this.id))
      this.option = {
        tooltip: {
          trigger: 'item',
          // formatter: `{a} <br/>{b} : {c} ({d}%)`
          formatter(param) {
            // console.log(JSON.stringify(param))
            return `${param.marker}` +
            `&nbsp;&nbsp;${param.name}：${param.value}&nbsp;&nbsp;(${param.data.label}%)<br>`
          }
        },
        legend: {
          bottom: 10,
          left: 'center'
        },
        series: [
          {
            name: '访问来源',
            type: 'pie',
            radius: '50%',
            center: ['50%', '40%'],
            data: this.propData.data,
            emphasis: {

            },
            label: {
              normal: {
                show: true,
                formatter: '{d}%' // 自定义显示格式(b:name, c:value, d:百分比) // 标签上的
              }
            }
          }
        ]
      }
      this.chart.setOption(this.option)
    }
  }
}
</script>
<style scoped>
</style>
